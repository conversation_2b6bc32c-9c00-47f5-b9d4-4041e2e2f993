# 非标项目开发流程

## 项目搭建

- 派生`BP-LOCAL`项目，新项目名称例:`BP-LOCAL-GANSU`（甘肃）。

- 标准版切换到`master`分支，`mvn install`。

- 修改`BP-LOCAL-GANSU`项目pom.xml中`parent`的版本为**标准版主分支版本**。

## Nacos配置

- 配置文件克隆`biaopu-zhzs-dev.yml`为`biaopu-local-dev.yml`

- 网关配置，检查`biaopu-gateway-dev.yml`是否存在一下配置

``````yml
        # 非标项目
        - id: biaopu-local
          uri: lb://biaopu-local
          predicates:
            - Path=/local/**
          filters:
            - StripPrefix=1  
``````

