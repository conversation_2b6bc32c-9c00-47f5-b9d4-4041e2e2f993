package com.hnbp.local.tssyfx.mapper;


import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.echarts.line.LineSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface TssyfxMapper {

    /**
     * 企业税收象限
     * @param params
     * @return
     */
    List<Map<String,Object>> getQuadrantAnalysis(Map<String,Object> params);

    /**
     * 企业注册数量变化-柱状图
     * @param params
     * @return
     */
    List<BarSeriesInitData> getEnterpriseRegistration(Map<String,Object> params);

    /**
     * 企业注册数量变化-柱状图-增长率
     * @param params
     * @return
     */
    List<LineSeriesInitData> getEnterpriseRegistrationGrowthRate(Map<String,Object> params);

    /**
     * 企业营收变化-柱状图
     * @param params
     * @return
     */
    List<BarSeriesInitData> getEnterpriseRevenue(Map<String,Object> params);

    /**
     * 企业税收变化-柱状图
     * @param params
     * @return
     */
    List<BarSeriesInitData> getEnterpriseTaxes(Map<String,Object> params);

    /**
     * 分地区-企业有效纳税-柱状图
     * @param params
     * @return
     */
    List<BarSeriesInitData> getDistrictEffectiveTaxEnterprise(Map<String,Object> params);

    /**
     * 分地区-企业税务登记-柱状图
     * @param params
     * @return
     */
    List<BarSeriesInitData> getRegionalTaxRegistrationEnterprises(Map<String,Object> params);


    List<BarSeriesInitData> getClassificationOfEnterprises(Map<String,Object> params);
    List<BarSeriesInitData> getEnterpriseHierarchicalTaxation(Map<String,Object> params);

    Map<String,Object> getfType(String fType);
}
