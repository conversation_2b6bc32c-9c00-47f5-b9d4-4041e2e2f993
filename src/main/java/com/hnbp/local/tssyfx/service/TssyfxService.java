package com.hnbp.local.tssyfx.service;


import com.hnbp.local.util.echarts.Bar.BarChart;

import java.util.List;
import java.util.Map;

public interface TssyfxService {

    List<Map<String,Object>> getQuadrantAnalysis(Map<String,Object> params);


    BarChart getEnterpriseRegistration(Map<String,Object> params);

    BarChart getEnterpriseRevenue(Map<String,Object> params);

    BarChart getEnterpriseTaxes(Map<String, Object> params);


    BarChart getDistrictEffectiveTaxEnterprise(Map<String, Object> params);

    BarChart getRegionalTaxRegistrationEnterprises(Map<String, Object> params);


    BarChart getClassificationOfEnterprises(Map<String, Object> params);

    BarChart getEnterpriseHierarchicalTaxation(Map<String, Object> params);


    Map<String, Object> gettextInfo(Map<String, Object> params);
}
