package com.hnbp.local.tssyfx.service.impl;

import com.hnbp.local.tssyfx.mapper.TssyfxMapper;
import com.hnbp.local.tssyfx.service.TssyfxService;
import com.hnbp.local.util.echarts.Bar.BarChart;
import com.hnbp.local.util.echarts.Bar.BarSeriesInitData;
import com.hnbp.local.util.util.ChartUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service
public class TssyfxServiceImpl implements TssyfxService {

    private static final DateTimeFormatter ISO_DATE = DateTimeFormatter.ofPattern("yyyy-MM");

    @Autowired
    private TssyfxMapper tssyfxMapper;


    @Override
    public List<Map<String, Object>> getQuadrantAnalysis(Map<String, Object> params) {
        //时间处理
        Map<String, Object> stringObjectMap = timeProcessing(params, "frkrq");
        List<Map<String, Object>> quadrantAnalysis = tssyfxMapper.getQuadrantAnalysis(stringObjectMap);

        return quadrantAnalysis;
    }

    @Override
    public BarChart getEnterpriseRegistration(Map<String, Object> params) {

        //组装柱状图 折线图数据
        List<BarSeriesInitData> enterpriseRegistration = tssyfxMapper.getEnterpriseRegistration(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(enterpriseRegistration, new HashMap());
        barChart.setTitle_text("企业注册数量变化");
        Map<String, Object> lineMap = CalculatedPolylineCharts(enterpriseRegistration, "增速");
        barChart.getSeries().add(lineMap);

        return barChart;
    }

    @Override
    public BarChart getEnterpriseRevenue(Map<String, Object> params) {
        List<BarSeriesInitData> enterpriseRevenue = tssyfxMapper.getEnterpriseRevenue(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(enterpriseRevenue, new HashMap());
        barChart.setTitle_text("企业营收额趋势");

        Map<String, Object> lineMap = CalculatedPolylineCharts(enterpriseRevenue, "增速");
        barChart.getSeries().add(lineMap);
        return barChart;
    }

    @Override
    public BarChart getEnterpriseTaxes(Map<String, Object> params) {
        List<BarSeriesInitData> enterpriseTaxes = tssyfxMapper.getEnterpriseTaxes(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(enterpriseTaxes, new HashMap());
        barChart.setTitle_text("企业税收变化");

        Map<String, Object> lineMap = CalculatedPolylineCharts(enterpriseTaxes, "增速");
        barChart.getSeries().add(lineMap);
        return barChart;
    }

    @Override
    public BarChart getDistrictEffectiveTaxEnterprise(Map<String, Object> params) {
        List<BarSeriesInitData> districtEffectiveTaxEnterprise = tssyfxMapper.getDistrictEffectiveTaxEnterprise(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(districtEffectiveTaxEnterprise, new HashMap());
        barChart.setTitle_text("分地区有效纳税企业数量");
        return barChart;
    }

    @Override
    public BarChart getRegionalTaxRegistrationEnterprises(Map<String, Object> params) {
        List<BarSeriesInitData> regionalTaxRegistrationEnterprises = tssyfxMapper.getRegionalTaxRegistrationEnterprises(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(regionalTaxRegistrationEnterprises, new HashMap());
        barChart.setTitle_text("分地区税务登记企业数量");
        return barChart;
    }

    @Override
    public BarChart getClassificationOfEnterprises(Map<String, Object> params) {
        List<BarSeriesInitData> classificationOfEnterprises = tssyfxMapper.getClassificationOfEnterprises(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(classificationOfEnterprises, new HashMap());
        barChart.setTitle_text("企业分级数量分析");
        return barChart;
    }

    @Override
    public BarChart getEnterpriseHierarchicalTaxation(Map<String, Object> params) {
        List<BarSeriesInitData> enterpriseHierarchicalTaxation = tssyfxMapper.getEnterpriseHierarchicalTaxation(params);
        BarChart barChart = ChartUtil.BieCharDataAssembly(enterpriseHierarchicalTaxation, new HashMap());
        barChart.setTitle_text("企业分级税收贡献分析");
        return barChart;
    }

    @Override
    public Map<String, Object> gettextInfo(Map<String, Object> params) {
        Map<String, Object> ftype = tssyfxMapper.getfType(params.get("ftype").toString());
        String key = params.get("key").toString();
        switch (key) {
            case "ztfx":

                CompletableFuture<BarChart> registrationFuture = CompletableFuture.supplyAsync(() -> this.getEnterpriseRegistration(params));
                CompletableFuture<BarChart> revenueFuture = CompletableFuture.supplyAsync(() -> this.getEnterpriseRevenue(params));
                CompletableFuture<BarChart> taxesFuture = CompletableFuture.supplyAsync(() -> this.getEnterpriseTaxes(params));


                try {
                    BarChart enterpriseRegistration = registrationFuture.get();
                    BarChart enterpriseRevenue = revenueFuture.get();
                    BarChart enterpriseTaxes = taxesFuture.get();


                    ftype.put("qyzc", enterpriseRegistration);
                    ftype.put("qyys", enterpriseRevenue);
                    ftype.put("qyss", enterpriseTaxes);
                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error executing futures", e);
                    Thread.currentThread().interrupt();
                }
                break;
            case "qyfx":
                CompletableFuture<BarChart> getDistrictEffectiveTaxEnterprise = CompletableFuture.supplyAsync(() -> this.getDistrictEffectiveTaxEnterprise(params));
                CompletableFuture<BarChart> getRegionalTaxRegistrationEnterprises = CompletableFuture.supplyAsync(() -> this.getRegionalTaxRegistrationEnterprises(params));

                try {
                    BarChart DistrictEffectiveTaxEnterprise = getDistrictEffectiveTaxEnterprise.get();
                    BarChart RegionalTaxRegistrationEnterprises = getRegionalTaxRegistrationEnterprises.get();

                    ftype.put("yxnsqy", DistrictEffectiveTaxEnterprise);
                    ftype.put("swdjqy", RegionalTaxRegistrationEnterprises);

                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error executing futures", e);
                    Thread.currentThread().interrupt();
                }
                break;
            case "qyfj":

                CompletableFuture<BarChart> getClassificationOfEnterprises = CompletableFuture.supplyAsync(() -> this.getClassificationOfEnterprises(params));
                CompletableFuture<BarChart> getEnterpriseHierarchicalTaxation = CompletableFuture.supplyAsync(() -> this.getEnterpriseHierarchicalTaxation(params));

                try {
                    BarChart ClassificationOfEnterprises = getClassificationOfEnterprises.get();
                    BarChart EnterpriseHierarchicalTaxation = getEnterpriseHierarchicalTaxation.get();

                    ftype.put("qyfjsl", ClassificationOfEnterprises);
                    ftype.put("qyfjns", EnterpriseHierarchicalTaxation);

                } catch (InterruptedException | ExecutionException e) {
                    log.error("Error executing futures", e);
                    Thread.currentThread().interrupt();
                }
                break;
        }
        return ftype;
    }

    /**
     * 跨月份查询同比处理开始时间结束时间
     *
     * @param params
     * @return
     */
    public Map<String, Object> timeProcessing(Map<String, Object> params, String keyName) {
        String frkrq = params.get(keyName).toString();
        String[] times = frkrq.split(" ~ ");
        String startTime = times[0];
        String endTime = times[1];

        YearMonth start = YearMonth.parse(startTime);
        YearMonth end = YearMonth.parse(endTime);
        YearMonth prevStartTime = start.minusYears(1);
        YearMonth prevEndTime = end.minusYears(1);

        String prevStart = prevStartTime.format(ISO_DATE);
        String prevEnd = prevEndTime.format(ISO_DATE);
        String currentStart = start.format(ISO_DATE);
        String currentEnd = end.format(ISO_DATE);

        params.put("prevStart", prevStart);
        params.put("prevEnd", prevEnd);
        params.put("currentStart", currentStart);
        params.put("currentEnd", currentEnd);

        return params;
    }

    /**
     * 计算每年的同比增长
     *
     * @param barSeriesInitData
     * @param name
     * @return
     */
    public Map<String, Object> CalculatedPolylineCharts(List<BarSeriesInitData> barSeriesInitData, String name) {
        Map<String, Object> seriesMap = new HashMap<>();
        seriesMap.put("name", name);
        seriesMap.put("type", "line");
        seriesMap.put("markPoint", new HashMap<>());
        List<Object> datas = new ArrayList<>();

        // 循环条件改为 i < barSeriesInitData.size() - 1
        for (int i = 0; i < barSeriesInitData.size() - 1; i++) {
            BarSeriesInitData prevData = barSeriesInitData.get(i);
            BarSeriesInitData currentData = barSeriesInitData.get(i + 1);

            Double prevValue = prevData.getyAxis();
            Double currentValue = currentData.getyAxis();

            // 防止除以零
            if (prevValue != null && prevValue != 0) {
                Double ratio = ((currentValue - prevValue) / prevValue) * 100;
                datas.add(String.format("%.2f", ratio));
            } else {
                datas.add(String.format("%.2f", 0.00)); // 或者添加一个默认值
            }
        }

        seriesMap.put("data", datas);
        return seriesMap;
    }
}
