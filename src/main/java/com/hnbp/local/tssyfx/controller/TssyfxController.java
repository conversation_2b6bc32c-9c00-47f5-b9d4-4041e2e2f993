package com.hnbp.local.tssyfx.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.tssyfx.service.TssyfxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/tssyfx")
@ResponseBody
public class TssyfxController {

    @Autowired
    private TssyfxService tssyfxService;

    @GetMapping("/getQuadrantAnalysis")
    public ResultMsg getQuadrantAnalysis(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getQuadrantAnalysis(params));
    }

    @GetMapping("/getEnterpriseRegistration")
    public ResultMsg getEnterpriseRegistration(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getEnterpriseRegistration(params));
    }

    @GetMapping("/getEnterpriseRevenue")
    public ResultMsg getEnterpriseRevenue(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getEnterpriseRevenue(params));
    }

    @GetMapping("/getEnterpriseTaxes")
    public ResultMsg getEnterpriseTaxes(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getEnterpriseTaxes(params));
    }

    @GetMapping("/getDistrictEffectiveTaxEnterprise")
    public ResultMsg getDistrictEffectiveTaxEnterprise(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getDistrictEffectiveTaxEnterprise(params));
    }

    @GetMapping("/getRegionalTaxRegistrationEnterprises")
    public ResultMsg getRegionalTaxRegistrationEnterprises(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getRegionalTaxRegistrationEnterprises(params));
    }

    @GetMapping("/getClassificationOfEnterprises")
    public ResultMsg getClassificationOfEnterprises(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getClassificationOfEnterprises(params));
    }

    @GetMapping("/getEnterpriseHierarchicalTaxation")
    public ResultMsg getEnterpriseHierarchicalTaxation(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.getEnterpriseHierarchicalTaxation(params));
    }

    @GetMapping("/gettextInfo")
    public ResultMsg gettextInfo(@RequestParam Map<String, Object> params) {
        return ResultMsg.success(tssyfxService.gettextInfo(params));
    }
}
