package com.hnbp.local.kshdp.mapper;

import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: Lsy
 */
@Mapper
public interface SsLargeScreenDisplayMapper {
    /**
     * 年度数据 税种 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByTaxType(Map<String, Object> parameterMap);


    /**
     * 税收按 市场主体 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getTaxDataByMainstay(Map<String, Object> parameterMap);


    /**
     * 年度数据 按行业 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByIndustry(Map<String, Object> parameterMap);

    /**
     * 年度数据 按省/地/区级 数据情况
     * @param parameterMap
     * @return
     */
    List<Map<String, Object>> getYearDataByLevel(Map<String, Object> parameterMap);



    /**
     * 年度数据 按区域 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByRegion(Map<String, Object> parameterMap);


    /**
     * 年度数据 按主体 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByMainstay(Map<String, Object> parameterMap);


    /**
     * 年度数据 按部门 数据情况
     * @param parameterMap
     * @return
     */
    List<BarSeriesInitData> getYearDataByDepartment(Map<String, Object> parameterMap);
}
