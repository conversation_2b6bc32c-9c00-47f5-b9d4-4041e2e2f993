package com.hnbp.local.kshdp.mapper;

import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface KshdpMapper {
    List<Map<String, Object>> nsgmfx(Map<String, Object> param);

    List<Map<String, Object>> getTaxDataByDepartment(Map<String, Object> parameterMap);

    List<BarSeriesInitData> getTaxDataByDepartmentTrend(Map<String, Object> param);

    List<Map<String, Object>> getQssjfhytj(Map<String, String> requestMap);

    List<BarSeriesInitData> nsgmfxTrend(Map<String, Object> param);

    List<Map<String, Object>> getTaxDataByPSTIndustry(Map<String, Object> stringObjectMap);

    List<BarSeriesInitData> getYearDataByPSTIndustry(Map<String, Object> parameterMap);

    List<Map<String, Object>> getTaxDataByPSTIndustryFxm(Map<String, Object> parameterMap);
}
