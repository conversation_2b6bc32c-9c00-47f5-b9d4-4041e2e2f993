package com.hnbp.local.kshdp.service;

import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.local.kshdp.mapper.KshdpMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class KshdpService {
    @Autowired
    private KshdpMapper kshdpMapper;

    public List<Map<String, Object>> nsgmfx(Map<String, Object> param) {
        List<Map<String, Object>> yearsData = kshdpMapper.nsgmfx(param);
        return yearsData;
    }

    public BarChart nsgmfxTrend(Map<String, Object> param) {
        List<BarSeriesInitData> yearsData = kshdpMapper.nsgmfxTrend(param);
        // 补全年份
        supplementYear(yearsData, param,param.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    public List<Map<String, Object>> getTaxDataByDepartment(Map<String, Object> parameterMap) {
        List<Map<String, Object>> departmentList = kshdpMapper.getTaxDataByDepartment(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = kshdpMapper.getTaxDataByDepartment(paramMap);

        // 税收总数
        AtomicReference<BigDecimal> totalFsssrhj = new AtomicReference<>(BigDecimal.ZERO);
        departmentList.forEach(map -> totalFsssrhj.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(map.get("fsssrhj")).orElse(0).toString()))));
        // 产值总数
        AtomicReference<BigDecimal> totalFcz = new AtomicReference<>(BigDecimal.ZERO);
        departmentList.forEach(map -> totalFcz.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(map.get("fcz")).orElse(0).toString()))));
        // 贷款余额总数
        AtomicReference<BigDecimal> totalFdkye = new AtomicReference<>(BigDecimal.ZERO);
        departmentList.forEach(map -> totalFdkye.updateAndGet(v -> v.add(new BigDecimal(Optional.ofNullable(map.get("fdkye")).orElse(0).toString()))));

        String mapKey = paramMap.get("fbmmc")==null?"fbmmc":"fhyfl";
        // 创建一个HashMap用于存储去年的数据，键为"fbmmc"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMapFsssrhj = new HashMap<>();
        lastYearList.stream().forEach(map -> lastYearMapFsssrhj.put(map.get(mapKey).toString(),map.get("fsssrhj")));

        Map<String, Object> lastYearMapFcz = new HashMap<>();
        lastYearList.stream().forEach(map -> lastYearMapFcz.put(map.get(mapKey).toString(),map.get("fcz")));

        Map<String, Object> lastYearMapFdkye = new HashMap<>();
        lastYearList.stream().forEach(map -> lastYearMapFdkye.put(map.get(mapKey).toString(),map.get("fdkye")));

        departmentList.stream().forEach(map -> {
            // 税收增幅、占比
            Object thisYearFsssrhj =  map.get("fsssrhj");
            Object lastYearFsssrhj =  lastYearMapFsssrhj.get(map.get(mapKey).toString());
            map.put("fhjzfb", getIncreasePercent(lastYearFsssrhj, thisYearFsssrhj));
            map.put("fhjzb", getProportion(totalFsssrhj.get(), thisYearFsssrhj));
            // 产值
            Object thisYearFcz =  map.get("fcz");
            Object lastYearFcz =  lastYearMapFcz.get(map.get(mapKey).toString());
            map.put("fczzfb", getIncreasePercent(lastYearFcz, thisYearFcz));
            map.put("fczzb", getProportion(totalFcz.get(), thisYearFcz));
            // 贷款余额
            Object thisYearFdkye =  map.get("fdkye");
            Object lastYearFdkye =  lastYearMapFdkye.get(map.get(mapKey).toString());
            map.put("fdkyezfb", getIncreasePercent(lastYearFdkye, thisYearFdkye));
            map.put("fdkyezb", getProportion(totalFdkye.get(), thisYearFdkye));
        });
        return departmentList;
    }

    public BarChart getTaxDataByDepartmentTrend(Map<String, Object> param) {
        List<BarSeriesInitData> yearsData = kshdpMapper.getTaxDataByDepartmentTrend(param);
        List<BarSeriesInitData> ssList = yearsData.stream().filter(bar -> "税收".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> czList = yearsData.stream().filter(bar -> "产值".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> dkyeList = yearsData.stream().filter(bar -> "贷款余额".equals(bar.getLegend())).collect(Collectors.toList());
        // 补全年份
        supplementYear(ssList, param,"税收");
        supplementYear(czList, param,"产值");
        supplementYear(dkyeList, param,"贷款余额");

        ssList.addAll(czList);
        ssList.addAll(dkyeList);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(ssList, extraParameter);
    }

    public List<Map<String, Object>> getTaxDataByPSTIndustry(Map<String, Object> parameterMap) {
        List<Map<String, Object>> industryList = kshdpMapper.getTaxDataByPSTIndustry(parameterMap);
        return industryList;
    }

    public BarChart getYearDataByPSTIndustry(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = kshdpMapper.getYearDataByPSTIndustry(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap, parameterMap.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    public List<Map<String, Object>> getTaxDataByPSTIndustryFxm(Map<String, Object> parameterMap) {
        return kshdpMapper.getTaxDataByPSTIndustryFxm(parameterMap);
    }

    public List<Map<String,Object>> getQssjfhytj(Map<String, String> requestMap) {
        return kshdpMapper.getQssjfhytj(requestMap);
    }

    /** 增减比例 */
    public double getIncreasePercent(BigDecimal lastYear, BigDecimal thisYear) {
        double percent = 0;
        if (lastYear!=null && thisYear!=null && lastYear.doubleValue() != 0) {
            percent = (thisYear.doubleValue() - lastYear.doubleValue()) / lastYear.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getIncreasePercent(Object lastYear, Object thisYear) {
        if (lastYear == null || thisYear == null) return 0;
        return this.getIncreasePercent(new BigDecimal(lastYear.toString()), new BigDecimal(thisYear.toString()));
    }
    /** 占比 */
    public double getProportion(BigDecimal total, BigDecimal num) {
        double percent = 0;
        if (total !=null && num!=null && total.doubleValue() != 0) {
            percent = num.doubleValue() / total.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getProportion(Object total, Object num) {
        if (total == null || num == null) return 0;
        return this.getProportion(new BigDecimal(total.toString()), new BigDecimal(num.toString()));
    }

    public List<BarSeriesInitData> supplementYear(List<BarSeriesInitData> dataList, Map<String, Object> parameterMap,String Legend) {
        Integer limit = 5;
        if (dataList.size()<limit){
            // x轴 存在的年份
            List<Integer> xAxis = dataList.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            Integer year = Integer.valueOf(parameterMap.get("fyear").toString());
            for (int i = 0; i < limit; i++){
                if (!xAxis.contains(year-i)){
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf((year-i)));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(Legend);
                    barSeriesInitData.setType("bar");

                    dataList.add(barSeriesInitData);
                }
            }
        }

        dataList.sort(Comparator.comparing(
                bar -> Integer.valueOf(bar.getxAxis())
        ));
        return dataList;
    }

}
