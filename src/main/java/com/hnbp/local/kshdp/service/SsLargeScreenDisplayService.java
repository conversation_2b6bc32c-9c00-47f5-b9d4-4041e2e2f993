package com.hnbp.local.kshdp.service;


import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData;
import com.hnbp.common.core.utils.zhzs.echarts.ChartUtil;
import com.hnbp.local.kshdp.mapper.SsLargeScreenDisplayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @version 1.0.00
 * @Annotation:
 * @Author: Lsy
 */

@Service
public class SsLargeScreenDisplayService {

    @Autowired
    private SsLargeScreenDisplayMapper sslargeScreenDisplayMapper;


    /**
     * 年度数据 按税种 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByTaxType(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByTaxType(parameterMap);
        List<BarSeriesInitData> ssList = yearsData.stream().filter(bar -> "税收".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> hsList = yearsData.stream().filter(bar -> "户数".equals(bar.getLegend())).collect(Collectors.toList());
        // 补全年份
        supplementYear(ssList, parameterMap,"税收");
        supplementYear(hsList, parameterMap,"户数");
        ssList.addAll(hsList);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(ssList, extraParameter);
    }

    /**
     * 税收按 市场主体 数据情况
     *
     * @param parameterMap
     * @return
     */

    public List<Map<String, Object>> getTaxDataByMainstay(Map<String, Object> parameterMap) {
        List<Map<String, Object>> taxTypeList = sslargeScreenDisplayMapper.getTaxDataByMainstay(parameterMap);
        Map<String, Object> paramMap = new HashMap<>(parameterMap);
        paramMap.put("fyear", parameterMap.get("lastYear"));
        // 上一年数据
        List<Map<String, Object>> lastYearList = sslargeScreenDisplayMapper.getTaxDataByMainstay(paramMap);

        // 总数
        AtomicReference<BigDecimal> total = new AtomicReference<>(BigDecimal.ZERO);
        taxTypeList.forEach(map -> total.updateAndGet(v -> v.add(new BigDecimal(map.get("fsssrhj").toString()))));

        // 创建一个HashMap用于存储去年的数据，键为"fjhxmmc"的值，值为"fsssrhj"的值
        Map<String, Object> lastYearMap = new HashMap<>();
        // 对去年的数据列表进行流式处理，将每个map中的"fjhxmmc"对应的值作为键，"fsssrhj"对应的值作为值，添加到lastYearMap中
        lastYearList.stream().forEach(map -> lastYearMap.put(map.get("fjhxmmc").toString(),map.get("fsssrhj")));

        taxTypeList.stream().forEach(map -> {
            Object thisYear =  map.get("fsssrhj");
            Object lastYear =  lastYearMap.get(map.get("fjhxmmc").toString());
            // 合计增幅
            map.put("fhjzfb", getIncreasePercent(lastYear, thisYear));

            // 占比
            map.put("fhjzb", getProportion(total.get(), thisYear));
        });
        return taxTypeList;
    }

    /**
     * 年度数据 按行业 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByIndustry(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByIndustry(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fhy").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按省/地/区级 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByLevel(Map<String, Object> parameterMap) {
        List<Map<String, Object>> yearDataByLevel = sslargeScreenDisplayMapper.getYearDataByLevel(parameterMap);
        // 需要取的列
        String levelKey = parameterMap.get("fxm").toString();
        Object fjbxmmc = parameterMap.getOrDefault("fjbxmmc",levelKey);
        // 组装 柱状图数据
        List<BarSeriesInitData> dataList = yearDataByLevel.stream().map(map -> {
            BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
            barSeriesInitData.setyAxis(Double.valueOf(map.get(levelKey).toString()));
            barSeriesInitData.setxAxis(map.get("fnf").toString());
            barSeriesInitData.setLegend(fjbxmmc.toString());
            barSeriesInitData.setType("bar");
            return barSeriesInitData;
        }).collect(Collectors.toList());

        // 补全年份
        supplementYear(dataList, parameterMap,fjbxmmc.toString());
        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(dataList, extraParameter);
    }

    /**
     * 年度数据 按区域 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByRegion(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByRegion(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按主体 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByMainstay(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByMainstay(parameterMap);
        // 补全年份
        supplementYear(yearsData, parameterMap,parameterMap.get("fxm").toString());

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(yearsData, extraParameter);
    }

    /**
     * 年度数据 按部门 数据情况
     *
     * @param parameterMap
     * @return
     */

    public BarChart getYearDataByDepartment(Map<String, Object> parameterMap) {
        List<BarSeriesInitData> yearsData = sslargeScreenDisplayMapper.getYearDataByDepartment(parameterMap);
        List<BarSeriesInitData> ssList = yearsData.stream().filter(bar -> "税收".equals(bar.getLegend())).collect(Collectors.toList());
        List<BarSeriesInitData> czList = yearsData.stream().filter(bar -> "产值".equals(bar.getLegend())).collect(Collectors.toList());
        // 补全年份
        supplementYear(ssList, parameterMap,"税收");
        supplementYear(czList, parameterMap,"产值");
        ssList.addAll(czList);

        //折线图额外要求markPoint
        HashMap extraParameter = new HashMap<>();
        //柱状图数据封装
        return ChartUtil.BieCharDataAssembly(ssList, extraParameter);

    }


    /**
     * 趋势年份不足时补充年份
     */
    public List<BarSeriesInitData> supplementYear(List<BarSeriesInitData> dataList, Map<String, Object> parameterMap,String Legend) {
        Integer limit = Integer.valueOf(parameterMap.get("limit").toString());
        if (dataList.size()<limit){
            // x轴 存在的年份
            List<Integer> xAxis = dataList.stream().map(bar ->Integer.valueOf(bar.getxAxis()))
                    .collect(Collectors.toList());
            Integer year = Integer.valueOf(parameterMap.get("fyear").toString());
            for (int i = 0; i < limit; i++){
                if (!xAxis.contains(year-i)){
                    BarSeriesInitData barSeriesInitData = new BarSeriesInitData();
                    barSeriesInitData.setxAxis(String.valueOf((year-i)));
                    barSeriesInitData.setyAxis(0.0);
                    barSeriesInitData.setLegend(Legend);
                    barSeriesInitData.setType("bar");

                    dataList.add(barSeriesInitData);
                }
            }
        }

        dataList.sort(Comparator.comparing(
                bar -> Integer.valueOf(bar.getxAxis())
        ));
        return dataList;
    }

    /** 增减比例 */
    public double getIncreasePercent(BigDecimal lastYear, BigDecimal thisYear) {
        double percent = 0;
        if (lastYear!=null && thisYear!=null && lastYear.doubleValue() != 0) {
            percent = (thisYear.doubleValue() - lastYear.doubleValue()) / lastYear.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getIncreasePercent(Object lastYear, Object thisYear) {
        if (lastYear == null || thisYear == null) return 0;
        return this.getIncreasePercent(new BigDecimal(lastYear.toString()), new BigDecimal(thisYear.toString()));
    }
    /** 占比 */
    public double getProportion(BigDecimal total, BigDecimal num) {
        double percent = 0;
        if (total !=null && num!=null && total.doubleValue() != 0) {
            percent = num.doubleValue() / total.doubleValue();
        }
        return Math.round(percent * 10000.0) / 100.0;
    }
    public double getProportion(Object total, Object num) {
        if (total == null || num == null) return 0;
        return this.getProportion(new BigDecimal(total.toString()), new BigDecimal(num.toString()));
    }
}
