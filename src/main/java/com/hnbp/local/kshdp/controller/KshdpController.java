package com.hnbp.local.kshdp.controller;

import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.echarts.Bar.BarChart;
import com.hnbp.local.kshdp.service.KshdpService;
import com.hnbp.local.kshdp.service.SsLargeScreenDisplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 可视化大屏
 */
@Controller
@RequestMapping("/kshdp")
public class KshdpController {
    @Autowired
    private KshdpService kshdpService;

    @Autowired
    private SsLargeScreenDisplayService sslargeScreenDisplayService;

    /** 趋势 年度数据 数据情况 */
    @RequestMapping("/getYearTrendData")
    @ResponseBody
    public ResultMsg getYearTrendData(@RequestParam Map<String, Object> parameterMap) throws IOException {
        Object typeObj = parameterMap.get("type");
        String type = null;
        if(typeObj!=null){
            type = typeObj.toString();
        }
        BarChart barChart = null;
        switch(type) {
            case "特色行业":
            case "行业":
                Map<String, Object> paramMap = new HashMap<>(parameterMap);
                paramMap.put("fhy", paramMap.get("fxm"));
                barChart = sslargeScreenDisplayService.getYearDataByIndustry(buildParam(paramMap));
                break;
            case "级别":
                barChart = sslargeScreenDisplayService.getYearDataByLevel(buildParam(parameterMap));
                break;
            case "区域":
                barChart = sslargeScreenDisplayService.getYearDataByRegion(buildParam(parameterMap));
                break;
            case "主体":
                barChart = sslargeScreenDisplayService.getYearDataByMainstay(buildParam(parameterMap));
                break;
            case "部门":
                barChart = kshdpService.getTaxDataByDepartmentTrend(buildParam(parameterMap));
                break;
            case "税种":
                barChart = sslargeScreenDisplayService.getYearDataByTaxType(buildParam(parameterMap));
                break;
            case "规模":
                barChart = kshdpService.nsgmfxTrend(buildParam(parameterMap));
                break;
            case "产业":
                barChart = kshdpService.getYearDataByPSTIndustry(buildParam(parameterMap));
                break;
        }

        return ResultMsg.success(barChart);
    }

    @RequestMapping("/nsgmfx")
    @ResponseBody
    public ResultMsg nsgmfx(@RequestParam Map<String, Object> param) throws Exception {
        List<Map<String, Object>> list = kshdpService.nsgmfx(param);
        return ResultMsg.success(list);
    }

    @RequestMapping("/nsgmfxTrend")
    @ResponseBody
    public ResultMsg nsgmfxTrend(@RequestParam Map<String, Object> param) throws Exception {
        BarChart barChart = kshdpService.nsgmfxTrend(param);
        return ResultMsg.success(barChart);
    }

    @RequestMapping("/getTaxDataByDepartment")
    @ResponseBody
    public ResultMsg getTaxDataByDepartment(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> list = kshdpService.getTaxDataByDepartment(buildParam(parameterMap));
        return ResultMsg.success(list);
    }

    @RequestMapping("/getTaxDataByDepartmentTrend")
    @ResponseBody
    public ResultMsg getTaxDataByDepartmentTrend(@RequestParam Map<String, Object> parameterMap) throws IOException {
        BarChart barChart = kshdpService.getTaxDataByDepartmentTrend(buildParam(parameterMap));
        return ResultMsg.success(barChart);
    }

    /**
     * 分产业
     * @param parameterMap
     * @return
     * @throws IOException
     */
    @RequestMapping("/getTaxDataByPSTIndustry")
    @ResponseBody
    public ResultMsg getTaxDataByPSTIndustry(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> list = kshdpService.getTaxDataByPSTIndustry(buildParam(parameterMap));
        return ResultMsg.success(list);
    }

    /**
     * 分产业
     * @param parameterMap
     * @return
     * @throws IOException
     */
    @RequestMapping("/getTaxDataByPSTIndustryFxm")
    @ResponseBody
    public ResultMsg getTaxDataByPSTIndustryFxm(@RequestParam Map<String, Object> parameterMap) throws IOException {
        List<Map<String, Object>> list = kshdpService.getTaxDataByPSTIndustryFxm(buildParam(parameterMap));
        return ResultMsg.success(list);
    }



    /**
     * 获取欠税数据-分行业
     * 从西双版纳迁移
     * @param requestMap
     * @return
     */
    @RequestMapping("/getQssjfhytj")
    @ResponseBody
    public ResultMsg getQssjfhytj(@RequestParam Map<String, String> requestMap) {
        return ResultMsg.success(kshdpService.getQssjfhytj(requestMap));
    }

    /**
     * 处理参数
     * @param parameterMap
     * @return
     */
    private Map<String,Object> buildParam(Map<String,Object> parameterMap){
        Map<String, Object> param = new HashMap<>();
        param.putAll(parameterMap);
        Object fyear = param.get("fyear");
        if(fyear!=null){
            String fyearStr = fyear.toString();
            // 前一年
            param.put("lastYear",Integer.parseInt(fyearStr)-1);
            param.put("fyear",Integer.parseInt(fyearStr));
        }
        Object fhy = param.get("fhy");
        if(fhy!=null){
            String fhyStr = fhy.toString();
            param.put("fhy",fhyStr);
            param.put("fhyList",fhyStr.split(","));
        }
        Object level = param.get("fxm");
        if(level!=null){
            String levelStr = level.toString();
            param.put("fxm",levelStr);
        }
        Object limit = param.get("limit");
        if(limit != null){
            String limitStr = limit.toString();
            param.put("limit",Integer.parseInt(limitStr));
        }else {
            param.put("limit",5);
        }

        return param;
    }
}
