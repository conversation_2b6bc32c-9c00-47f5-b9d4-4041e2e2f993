package com.hnbp.local.czdp.controller;


import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.czdp.service.CzLargeScreenDisplayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("czdpzs")
@ResponseBody
public class CzLargeScreenDisplayController {

    @Autowired
    private CzLargeScreenDisplayService czlargeScreenDisplayService;


    /**
     * 一般公共预算收支情况表
     */
    @RequestMapping("/getIncomeAndExpensesTable")
    public ResultMsg getIncomeAndExpensesTable(@RequestParam Map<String, Object> params) {
        List<Map<String, Object>> res = czlargeScreenDisplayService.getIncomeAndExpensesTable(params);
        return ResultMsg.success(res);
    }
}