package com.hnbp.local.czdp.service;


import com.hnbp.local.czdp.mapper.CzLargeScreenDisplayMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CzLargeScreenDisplayService {

    @Autowired
    private CzLargeScreenDisplayMapper czlargeScreenDisplayMapper;

    public List<Map<String, Object>> getIncomeAndExpensesTable(Map<String, Object> params) {
        return czlargeScreenDisplayMapper.getIncomeAndExpensesTable( params);
    }


}
