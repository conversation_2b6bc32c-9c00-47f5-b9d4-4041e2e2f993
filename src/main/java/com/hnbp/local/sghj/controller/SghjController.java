package com.hnbp.local.sghj.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.sghj.model.QueryWarninganalysis;
import com.hnbp.local.sghj.service.SghjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("sghj")
@ResponseBody
public class SghjController {
    @Autowired
    private SghjService sghjService;

    @RequestMapping("sgxmyj")
    @ResponseBody
    public ResultMsg sgxmyj(@RequestBody Map<String, Object> parameterMap) {
        PageInfo<Map<String, Object>> result = sghjService.sgxmyj(parameterMap);
        return ResultMsg.success(result.getList(), result.getTotal());
    }

    @RequestMapping("sgxmyj-detail")
    @ResponseBody
    public ResultMsg sgxmyjDetail(@RequestBody Map<String, Object> parameterMap) {
        PageInfo<Map<String, Object>> result = sghjService.sgxmyjDetail(parameterMap);
        return ResultMsg.success(result.getList(), result.getTotal());
    }

    @RequestMapping("zbqygcxmyj")
    @ResponseBody
    public ResultMsg zbqygcxmyj(@RequestBody Map<String, Object> parameterMap) {
        PageInfo<Map<String, Object>> result = sghjService.zbqygcxmyj(parameterMap);
        return ResultMsg.success(result.getList(), result.getTotal());
    }

    @RequestMapping("zbqygcxmyj-detail")
    @ResponseBody
    public ResultMsg zbqygcxmyjDetail(@RequestBody Map<String, Object> parameterMap) {
        PageInfo<Map<String, Object>> result = sghjService.zbqygcxmyjDetail(parameterMap);
        return ResultMsg.success(result.getList(), result.getTotal());
    }

    /**
     * @Description: 缴税信息明细
     */
    @RequestMapping("findTaxDetails_bzb")
    @ResponseBody
    public ResultMsg findTaxDetails_bzb(QueryWarninganalysis parameterMap) {
        parameterMap.paramProcess();
        //参数处理
        PageInfo pageInfo = sghjService.findTaxDetails_bzb(parameterMap);
        return ResultMsg.success(pageInfo.getList(),pageInfo.getTotal());
    }
}
