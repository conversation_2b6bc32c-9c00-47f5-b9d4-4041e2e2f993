package com.hnbp.local.sghj.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.sghj.mapper.SghjMapper;
import com.hnbp.local.sghj.model.QueryWarninganalysis;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class SghjService {
    @Autowired
    private SghjMapper sghjMapper;

    public PageInfo<Map<String, Object>> sgxmyj(Map<String, Object> parameterMap) {
        PageHelper.startPage(Integer.parseInt(parameterMap.get("page").toString()), Integer.parseInt(parameterMap.get("limit").toString()));
        return sghjMapper.sgxmyj(parameterMap).toPageInfo();
    }

    public PageInfo<Map<String, Object>> sgxmyjDetail(Map<String, Object> parameterMap) {
        PageHelper.startPage(Integer.parseInt(parameterMap.get("page").toString()), Integer.parseInt(parameterMap.get("limit").toString()));
        return sghjMapper.sgxmyjDetail(parameterMap).toPageInfo();
    }

    public PageInfo<Map<String, Object>> zbqygcxmyj(Map<String, Object> parameterMap) {
        PageHelper.startPage(Integer.parseInt(parameterMap.get("page").toString()), Integer.parseInt(parameterMap.get("limit").toString()));
        return sghjMapper.zbqygcxmyj(parameterMap).toPageInfo();
    }

    public PageInfo<Map<String, Object>> zbqygcxmyjDetail(Map<String, Object> parameterMap) {
        PageHelper.startPage(Integer.parseInt(parameterMap.get("page").toString()), Integer.parseInt(parameterMap.get("limit").toString()));
        return sghjMapper.zbqygcxmyjDetail(parameterMap).toPageInfo();
    }

    public PageInfo findTaxDetails_bzb(QueryWarninganalysis pagingParameter) {
        PageHelper.startPage(pagingParameter.getPage(), pagingParameter.getLimit());
        return new PageInfo(sghjMapper.findTaxDetails_bzb(pagingParameter));
    }
}
