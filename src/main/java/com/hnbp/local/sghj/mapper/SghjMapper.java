package com.hnbp.local.sghj.mapper;

import com.github.pagehelper.Page;
import com.hnbp.local.sghj.model.QueryWarninganalysis;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface SghjMapper {
    Page<Map<String, Object>> sgxmyj(Map<String, Object> parameterMap);

    Page<Map<String, Object>> sgxmyjDetail(Map<String, Object> parameterMap);

    Page<Map<String, Object>> zbqygcxmyj(Map<String, Object> parameterMap);

    Page<Map<String, Object>> zbqygcxmyjDetail(Map<String, Object> parameterMap);

    List findTaxDetails_bzb(QueryWarninganalysis pagingParameter);
}
