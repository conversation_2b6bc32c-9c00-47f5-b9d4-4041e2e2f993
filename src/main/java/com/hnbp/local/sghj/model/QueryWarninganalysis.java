package com.hnbp.local.sghj.model;

import com.hnbp.common.core.utils.StringUtils;
import lombok.Data;

import java.util.List;

/*
 *@title QueryWarninganalysis
 *@description
 *<AUTHOR>
 *@create 2023/7/27 20:40
 */
@Data
public class QueryWarninganalysis {

    //预警模块
    private String fyear;//开始年份
    private String flastyear;//去年份
    private String fyear_e;//结束年份
    private String fmonth_s;//开始月份
    private String fmonth_e;//结束月份
    private String fnsrmc;//纳税人名称
    private String fshxydm;//社会信用代码

    private String fzspm;//征收品目

    private String fplz;//偏离值
    private String faverage;//均价
    private String fskzb;//刷卡占比
    private String fxsezb;//销售额占比
    private String fsysl;//适用税额
    private String fzdysl;//自定义税率
    private String fzdyslxgm;//自定义税率小规模
    private String frjy;//酒店人均元
    private String fqzd;//起征点
    private String fbmf;//报名费
    private String fhbsse;//环保税税额
    private String fhnlcl;//环保税税额

    private String fswzc;//是否税务注册
    private String fyjfs;//预警方式
    private String fskssqq_s;//预警方式
    private String fskssqq_e;//预警方式
    private String frkrq_s;//预警方式
    private String frkrq_e;//预警方式
    private String fssqy;//所属区域
    private List<String> fssqyList; // 所属区域List
    private String fhyml;//行业门类
    private List<String> fhymlList; //行业门类List
    private String fzsxm;//征收项目
    private List<String> fzsxmList;//征收项目List
    private String userid;//用户id
    private String tdcryj;//土地出让

    private Integer page;
    private Integer limit;

    //加油站
    private String fjer;
    private String fjwu;
    private String fjba;
    private String fcy;
    private String fqydw;
    private String fcydw;

    //房土两税
    private String fcztdsysjj;//城镇所得税均价
    private String ffcsjj;//房产税均价
    private String fsfjn;//分析口径

    //砂石
    private String fssy;//每公斤炸药/公斤砂石
    private String fzzssl;//增值税税率
    private String fqysdssl;//企业所得税税率
    private String ffjssl;//附加税税率

    //最新时间
    private String ftablename;//表名
    private String ffhzt;//表名
    private String fdata;//表名
    private String fyjkzs;//表名

    Boolean enZspm; //是否使用征收品目字段

    public void paramProcess(){
        this.fssqyList = StringUtils.convertStringToList(this.fssqy, String::valueOf);
        this.fzsxmList = StringUtils.convertStringToList(this.fzsxm, String::valueOf);
        this.fhymlList = StringUtils.convertStringToList(this.fhyml, String::valueOf);
        String fyear = this.fyear;
        if (StringUtils.isNotEmpty(fyear)) {
            this.flastyear = String.valueOf(Integer.parseInt(fyear) - 1);
        }
    }
}
