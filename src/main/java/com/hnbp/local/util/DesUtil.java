package com.hnbp.local.util;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.IOException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * DES加密 解密算法
 * 注意：由于DES秘钥的长度为56位，24小时内即可破解
 * 	         推荐在简单应用和安全度不高的系统上使用此加密解密算法
 *
 * <AUTHOR>
 */
public class DesUtil {

    private final static String DES = "DES";
    private final static String ENCODE = "UTF-8";//编码格式
    private final static String KEY = "jPQQqFT3lwg=";//秘钥（长度8个字节，超过8个，以前8个为准）

    public static void main(String[] args) throws Exception {
        String data = "000000";
        System.out.println("加密后的数据： " + encrypt(data, KEY));
        System.out.println("解密后的数据： " + decrypt(encrypt(data, KEY), KEY));
        System.out.println("加密后的数据： " + encrypt(data));
        System.out.println("解密后的数据： " + decrypt(encrypt(data)));

    }

    /**
     * Description 使用 默认key 加密
     *
     * @return String
     */
    public static String encrypt(String data){
        String strs =null;
        try {
            byte[] bt = encrypt(data.getBytes(ENCODE), KEY.getBytes(ENCODE));
            strs = Base64.getEncoder().encodeToString(bt);
        }catch (Exception e){
            e.printStackTrace();
        }
        return strs;
    }

    /**
     * Description 使用 默认key 解密
     *
     * @param data
     * @return String
     */
    public static String decrypt(String data) throws IOException, Exception {
        if (data == null)
            return null;
        byte[] buf = Base64.getDecoder().decode(data);
        byte[] bt = decrypt(buf, KEY.getBytes(ENCODE));
        return new String(bt, ENCODE);
    }

    /**
     * Description 根据键值进行加密
     *
     * @param data
     * @param key 加密键byte数组
     * @return String
     * @throws Exception
     */
    public static String encrypt(String data, String key) throws Exception {
        byte[] bt = encrypt(data.getBytes(ENCODE), KEY.getBytes(ENCODE));
        String strs = Base64.getEncoder().encodeToString(bt);
        return strs;
    }

    /**
     * Description 根据键值进行解密
     *
     * @param data
     * @param key 加密键byte数组
     * @return String
     * @throws IOException
     * @throws Exception
     */
    public static String decrypt(String data, String key) throws IOException,
            Exception {
        if (data == null)
            return null;
        byte[] buf = Base64.getDecoder().decode(data);
        byte[] bt = decrypt(buf, key.getBytes(ENCODE));
        return new String(bt, ENCODE);
    }

    /**
     * Description 根据键值进行加密
     *
     * @param data 加密数据byte数组
     * @param key  加密键byte数组
     * @return byte[]
     * @throws Exception
     */
    private static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        // 生成一个可信任的随机数源
        SecureRandom sr = new SecureRandom();

        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);

        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);

        // Cipher对象实际完成加密操作
        Cipher cipher = Cipher.getInstance(DES);

        // 用密钥初始化Cipher对象
        cipher.init(Cipher.ENCRYPT_MODE, securekey, sr);

        return cipher.doFinal(data);
    }

    /**
     * Description 根据键值进行解密
     *
     * @param data
     * @param key 加密键byte数组
     * @return byte[]
     * @throws Exception
     */
    private static byte[] decrypt(byte[] data, byte[] key) throws Exception {
        // 生成一个可信任的随机数源
        SecureRandom sr = new SecureRandom();

        // 从原始密钥数据创建DESKeySpec对象
        DESKeySpec dks = new DESKeySpec(key);

        // 创建一个密钥工厂，然后用它把DESKeySpec转换成SecretKey对象
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES);
        SecretKey securekey = keyFactory.generateSecret(dks);

        // Cipher对象实际完成解密操作
        Cipher cipher = Cipher.getInstance(DES);

        // 用密钥初始化Cipher对象
        cipher.init(Cipher.DECRYPT_MODE, securekey, sr);

        return cipher.doFinal(data);
    }
}
