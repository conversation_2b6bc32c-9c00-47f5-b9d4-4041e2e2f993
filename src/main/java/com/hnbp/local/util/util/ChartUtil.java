package com.hnbp.local.util.util;

import com.hnbp.local.util.echarts.Bar.*;
import com.hnbp.local.util.echarts.line.LineChart;
import com.hnbp.local.util.echarts.line.LineSeriesData;
import com.hnbp.local.util.echarts.line.LineSeriesInitData;
import com.hnbp.local.util.echarts.pie.PieChartsSeriesData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/4 15:10
 * @Title
 */
public class ChartUtil {
    /**
     * 组装饼图 PieLegend
     *
     * @param parameter
     * @return
     */
    public static List pieLegendDataAssembly(List<PieChartsSeriesData> parameter) {
        List returnVal = new ArrayList();
        int count = parameter.size();
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                returnVal.add(parameter.get(i).getName());
            }
        }
        return returnVal;
    }

    /**
     * 组装饼图 LineLegend
     *
     * @param parameter
     * @return
     */
    public static List lineLegendDataAssembly(List<LineSeriesData> parameter) {
        List returnVal = new ArrayList();
        int count = parameter.size();
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                returnVal.add(parameter.get(i).getName());
            }
        }
        return returnVal;
    }

    /**
     * 柱状图数据组装
     *
     * @param barSeriesInitData
     * @return
     */
    public static BarChart BieCharDataAssembly(List<BarSeriesInitData> barSeriesInitData, Map extraParameter) {
        //获取柱状图 legend_data
        BarChart barChart = new BarChart();
        if (barSeriesInitData.size() > 0) {
            HashMap bieLegendDataAssembly = BieLegendDataAssembly(barSeriesInitData);
            barChart.setLegend_data((List) bieLegendDataAssembly.get("legend"));
            barChart.setxAxis_data((List) bieLegendDataAssembly.get("xAxis"));
            barChart.setSeries(BieLegendSeries(barSeriesInitData, bieLegendDataAssembly, extraParameter));
        }
        return barChart;
    }

    /**
     * 柱状图数据组装
     *
     * @param barSeriesInitData
     * @return
     */
    public static BarChart BieCharDataAssembly(HashMap bieLegendDataAssembly, List<BarSeriesInitData> barSeriesInitData, Map extraParameter) {
        //获取柱状图 legend_data
        BarChart barChart = new BarChart();
        if (barSeriesInitData.size() > 0) {
            barChart.setLegend_data((List) bieLegendDataAssembly.get("legend"));
            barChart.setxAxis_data((List) bieLegendDataAssembly.get("xAxis"));
            barChart.setSeries(BieLegendSeries(barSeriesInitData, bieLegendDataAssembly, extraParameter));
        }
        return barChart;
    }

    /**
     * 组装柱状图 Legend  xAxis
     *
     * @param parameter
     * @return
     */
    public static HashMap BieLegendDataAssembly(List<BarSeriesInitData> parameter) {
        HashMap map = new HashMap(); //参数 legend  xAxis
        int count = parameter.size();
        if (count > 0) {
            List legendVal = new ArrayList();
            List<String> xAxisVal = new ArrayList();
            List<String> typeVal = new ArrayList();
            for (int i = 0; i < count; i++) {
                String legend = parameter.get(i).getLegend();
                String xAxis = parameter.get(i).getxAxis();
                String type = parameter.get(i).getType();
                if (!legendVal.contains(legend)) {
                    legendVal.add(legend);
                    typeVal.add(type);
                }
                if (!xAxisVal.contains(xAxis)) {
                    xAxisVal.add(xAxis);
                }
            }
            map.put("legend", legendVal);
            map.put("xAxis", xAxisVal);
            map.put("type", typeVal);
        }
        return map;
    }

    /**
     * 组装柱状图 Series
     *
     * @param barSeriesInitData
     * @param bieLegendDataAssembly
     * @return
     */
    public static List BieLegendSeries(List<BarSeriesInitData> barSeriesInitData, Map bieLegendDataAssembly, Map extraParameter) {
        List returnVal = new ArrayList();
        int barSeriesInitDataCount = barSeriesInitData.size();
        List bieLegendDataAssemblyLegend = (List) bieLegendDataAssembly.get("legend");
        int bieLegendDataAssemblyLegendCount = bieLegendDataAssemblyLegend.size();

        List bieLegendDataAssemblyxAxis = (List) bieLegendDataAssembly.get("xAxis");
        int bieLegendDataAssemblyxAxisCount = bieLegendDataAssemblyxAxis.size();

        List bieLegendDataAssemblyType = (List) bieLegendDataAssembly.get("type");
        int bieLegendDataAssemblyTypeCount = bieLegendDataAssemblyType.size();

        //添加servicsData
        for (int j = 0; j < bieLegendDataAssemblyLegendCount; j++) {
            String leng = (String) bieLegendDataAssemblyLegend.get(j);
            String type = (String) bieLegendDataAssemblyType.get(j);
            List xdata = new ArrayList();
            for (int i = 0; i < bieLegendDataAssemblyxAxisCount; i++) {
                xdata.add(0);   //初始化数组全为0
            }
            for (int i = 0; i < barSeriesInitDataCount; i++) {
                BarSeriesInitData dataMap = barSeriesInitData.get(i);
                String legend = dataMap.getLegend();
                if (leng.equals(legend)) {
                    int num = bieLegendDataAssemblyxAxis.indexOf(dataMap.getxAxis());   //获取X轴在数组中对应坐标
                    Double yAxis = dataMap.getyAxis();
                    //标签不属于X轴
                    if (num != -1) {
                        xdata.set(num, yAxis);
                    } else {
                        System.out.println("X轴值:" + dataMap.getxAxis() + "    不在标签内");
                    }
                }
            }
            BarSeriesData barSeriesData = new BarSeriesData();
            barSeriesData.setName(leng);
            barSeriesData.setData(xdata);
            if (leng != null && leng.equals("增幅")) {
                barSeriesData.setType("line");
            } else if (type != null && type.equals("line")) {
                barSeriesData.setType("line");
            }

            //判断是否需要添加markPoint参数
            if (extraParameter.containsKey("markPoint")) {
                ArrayList list = new ArrayList<>();
                BarMarkPoint barMarkPoint = new BarMarkPoint();
                BarMarkPointData barMarkPointData = new BarMarkPointData();
                barMarkPointData.setType("max");
                barMarkPointData.setName("最大值");
                list.add(barMarkPointData);//添加MarkPointData参数
                BarMarkPointData barMarkPointDatamin = new BarMarkPointData();
                barMarkPointDatamin.setType("min");
                barMarkPointDatamin.setName("最小值");
                list.add(barMarkPointDatamin);//添加MarkPointData参数

                barMarkPoint.setData(list);
                barSeriesData.setMarkPoint(barMarkPoint);
            }

            returnVal.add(barSeriesData);
        }
        return returnVal;
    }


    /**
     * 折线图数据组装
     *
     * @param lineSeriesInitData
     * @return
     */
    public static LineChart lineCharDataAssembly(List<LineSeriesInitData> lineSeriesInitData, Map extraParameter) {
        //获取柱状图 legend_data
        LineChart LineChart = new LineChart();
        if (lineSeriesInitData.size() > 0) {
            HashMap LineLegendDataAssembly = LineLegendDataAssembly(lineSeriesInitData);
            LineChart.setLegend_data((List) LineLegendDataAssembly.get("legend"));
            LineChart.setxAxis_data((List) LineLegendDataAssembly.get("xAxis"));
            LineChart.setSeries(LineLegendSeries(lineSeriesInitData, (List) LineLegendDataAssembly.get("legend"), extraParameter));
        }
        return LineChart;
    }

    /**
     * 组装折线图 Legend  xAxis
     *
     * @param parameter
     * @return
     */
    public static HashMap LineLegendDataAssembly(List<LineSeriesInitData> parameter) {
        HashMap map = new HashMap(); //参数 legend  xAxis
        int count = parameter.size();
        if (count > 0) {
            List legendVal = new ArrayList();
            List xAxisVal = new ArrayList();
            for (int i = 0; i < count; i++) {
                String legend = parameter.get(i).getLegend();
                String xAxis = parameter.get(i).getxAxis();
                if (!xAxisVal.contains(xAxis)) {
                    xAxisVal.add(xAxis);
                }
                if (!legendVal.contains(legend)) {
                    legendVal.add(legend);
                }
            }
            map.put("legend", legendVal);
            map.put("xAxis", xAxisVal);
        }
        return map;
    }


    /**
     * 组装折线图 Series
     *
     * @param lineSeriesInitData,     lineLegendDataAssembly,
     * @param extraParameter:空集合待后面调用
     * @return java.util.List
     * @Title LineLegendSeries
     * @date 2019/7/5 18:02
     * <AUTHOR>
     */
    public static List LineLegendSeries(List<LineSeriesInitData> lineSeriesInitData, List lineLegendDataAssembly, Map extraParameter) {
        List returnVal = new ArrayList();
        int LineSeriesInitDataCount = lineSeriesInitData.size();
        int LineLegendDataAssemblyCount = lineLegendDataAssembly.size();
        //添加servicsData
        for (int j = 0; j < LineLegendDataAssemblyCount; j++) {
            String leng = (String) lineLegendDataAssembly.get(j);
            List xdata = new ArrayList();
            for (int i = 0; i < LineSeriesInitDataCount; i++) {
                LineSeriesInitData dataMap = lineSeriesInitData.get(i);
                String legend = dataMap.getLegend();
                if (leng == null || leng.equals(legend)) {
                    String yAxis = dataMap.getyAxis();
                    xdata.add(yAxis);
                }
            }
            LineSeriesData lineSeriesData = new LineSeriesData();
            lineSeriesData.setName(leng);
            lineSeriesData.setData(xdata);
            //判断是否需要添加markPoint参数
            if (extraParameter.containsKey("markPoint")) {
                ArrayList list = new ArrayList<>();
                BarMarkPoint barMarkPoint = new BarMarkPoint();
                BarMarkPointData barMarkPointData = new BarMarkPointData();
                barMarkPointData.setType("max");
                barMarkPointData.setName("最大值");
                list.add(barMarkPointData);//添加MarkPointData参数
                BarMarkPointData barMarkPointDatamin = new BarMarkPointData();
                barMarkPointDatamin.setType("min");
                barMarkPointDatamin.setName("最小值");
                list.add(barMarkPointDatamin);//添加MarkPointData参数

                barMarkPoint.setData(list);
                lineSeriesData.setMarkPoint(barMarkPoint);
            }
            returnVal.add(lineSeriesData);
        }
        return returnVal;
    }


}
