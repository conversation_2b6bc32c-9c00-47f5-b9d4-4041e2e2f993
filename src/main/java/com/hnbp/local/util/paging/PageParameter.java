package com.hnbp.local.util.paging;

import java.util.HashMap;
import java.util.Map;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/9 14:04
 * @Title
 */
public class PageParameter {
    private Integer page; //页码
    private Integer limit;  //分页长度
    private boolean isPaging = true;//是否分页
    private Object params;//请求参数
    /**
     * 查询字段（用于最后一行拼接合计）
     **/
    private Map<String, Object> sumMap = new HashMap<>();

    public Map<String, Object> getSumMap() {
        return sumMap;
    }

    /**
     * 设置字段集合值
     *
     * @param sumMap key:field对应需要求合计的list<string>字段集合
     *               key:isOne对应是否第一行添加合计 true : false
     *               key:fname对应显示合计的结果集key（纳税人名称，fname）
     */
    public void setSumMap(Map<String, Object> sumMap) {
        this.sumMap = sumMap;
    }

    public boolean isPaging() {
        return isPaging;
    }

    public void setPaging(boolean paging) {
        isPaging = paging;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Object getParams() {
        return params;
    }

    public void setParams(Object params) {
        this.params = params;
    }
}
