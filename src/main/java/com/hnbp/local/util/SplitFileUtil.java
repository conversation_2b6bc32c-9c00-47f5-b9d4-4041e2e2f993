package com.hnbp.local.util;


import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class SplitFileUtil {
    public SplitFileUtil() {
    }

    public static List<String> splitBySize(String filePath, String fileName, int byteSize) throws IOException {
        List<String> parts = new ArrayList();
        File file = new File(fileName);
        int count = (int)Math.ceil((double)file.length() / (double)byteSize);
        int countLen = (count + "").length();
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(count, count * 3, 1L, TimeUnit.SECONDS, new ArrayBlockingQueue(count * 2));

        for(int i = 0; i < count; ++i) {
            String partFileName = file.getName() + "." + FileUtil.leftPad(i + 1 + "", countLen, '0') + ".part";
            threadPool.execute(new SplitRunnable(byteSize, i * byteSize, partFileName, file, filePath));
            parts.add(partFileName);
        }

        return parts;
    }

    public static void mergePartFiles(String dirPath, String partFileSuffix, int partFileSize, String mergeFileName) throws IOException {
        ArrayList<File> partFiles = FileUtil.getDirFiles(dirPath, partFileSuffix);
        RandomAccessFile randomAccessFile = new RandomAccessFile(mergeFileName, "rw");
        randomAccessFile.setLength((long)(partFileSize * (partFiles.size() - 1)) + ((File)partFiles.get(partFiles.size() - 1)).length());
        randomAccessFile.close();
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(partFiles.size(), partFiles.size() * 3, 1L, TimeUnit.SECONDS, new ArrayBlockingQueue(partFiles.size() * 2));

        for(int i = 0; i < partFiles.size(); ++i) {
            threadPool.execute(new MergeRunnable((long)(i * partFileSize), mergeFileName, (File)partFiles.get(i)));
        }

    }
}

