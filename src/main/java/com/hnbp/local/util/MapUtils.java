package com.hnbp.local.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @version 1.0.0
 * @createTime 2021年03月02日 11:25:00
 */
@Configuration
public class MapUtils {
    @Autowired
    private BeanMapUtils beanMapUtils;

    /**
     * list<Map>  key 转小写
     *
     * @param maps
     * @return
     */
    public List<Map> getMapsToLower(List<Map> maps) {
        return maps.parallelStream().map(it -> {
            Map<String, Object> map = new LinkedHashMap<>();
            Iterator iterator = it.keySet().iterator();
            while (iterator.hasNext()) {
                String next = (String) iterator.next();
                map.put(next.toLowerCase(), it.get(next));
            }
            return map;
        }).collect(Collectors.toList());
    }

    public List getMapsToBean(List<Map> maps, Class clazz) {
        List list = new ArrayList();
        for (int i = 0; i < maps.size(); i++) {
            list.add(beanMapUtils.mapToBean(maps.get(i), clazz));
        }
        return list;
    }

    public List<Map> getMaps(List<Map> maps, String pre, String f_sys_no) {
        int i = 0;
        List<Map> list = new ArrayList<>();
        for (int i1 = 0; i1 < maps.size(); i1++) {
            Map it = maps.get(i);

            Map<String, Object> map = new LinkedHashMap<>();
            Iterator iterator = it.keySet().iterator();
            while (iterator.hasNext()) {
                Object next = iterator.next();
                map.put(pre + next, it.get(next));
            }
            map.put("f_sys_no", i1);
            list.add(map);


        }
        return list;
    }

    /**
    Map key转小写
     */
    public static Map<String, Object> transformUpperCase(Map<String, Object> orgMap) {
        Map<String, Object> resultMap = new HashMap<>();

        if (orgMap == null || orgMap.isEmpty()) {
            return resultMap;
        }

        Set<String> keySet = orgMap.keySet();
        for (String key : keySet) {
            String newKey = key.toLowerCase();
            newKey = newKey.replace("_", "");

            resultMap.put(newKey, orgMap.get(key));
        }

        return resultMap;
    }
}
