package com.hnbp.local.util;


import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.security.InvalidKeyException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.SignatureException;
import java.security.UnrecoverableKeyException;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

public class CertifacateUtil {
    private static final String CERT_TYPE = "X.509";

    public CertifacateUtil() {
    }

    public static KeyStore getKeyStore(String storepass, String keystorePath) throws IOException {
        InputStream inputStream = null;

        try {
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            inputStream = new FileInputStream(keystorePath);
            keyStore.load(inputStream, storepass.toCharArray());
            KeyStore var4 = keyStore;
            return var4;
        } catch (NoSuchAlgorithmException | CertificateException | IOException | KeyStoreException e) {
            ((Exception)e).printStackTrace();
        } finally {
            if (null != inputStream) {
                inputStream.close();
            }

        }

        return null;
    }

    public static PrivateKey getPrivateKey(KeyStore keyStore, String alias, String password) {
        try {
            return (PrivateKey)keyStore.getKey(alias, password.toCharArray());
        } catch (KeyStoreException | NoSuchAlgorithmException | UnrecoverableKeyException e) {
            ((GeneralSecurityException)e).printStackTrace();
            return null;
        }
    }

    public static PublicKey getPublicKey(Certificate certificate) {
        return certificate.getPublicKey();
    }

    public static X509Certificate getCertificateByKeystore(KeyStore keyStore, String alias) {
        try {
            return (X509Certificate)keyStore.getCertificate(alias);
        } catch (KeyStoreException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static X509Certificate getCertificateByCertPath(String path) throws IOException {
        InputStream inputStream = null;

        try {
            CertificateFactory factory = CertificateFactory.getInstance("X.509");
            inputStream = new FileInputStream(path);
            Certificate certificate = factory.generateCertificate(inputStream);
            X509Certificate var4 = (X509Certificate)certificate;
            return var4;
        } catch (IOException | CertificateException e) {
            ((Exception)e).printStackTrace();
        } finally {
            if (null != inputStream) {
                inputStream.close();
            }

        }

        return null;
    }

    public static byte[] sign(X509Certificate certificate, PrivateKey privateKey, byte[] plainText) {
        try {
            Signature signature = Signature.getInstance(certificate.getSigAlgName());
            signature.initSign(privateKey);
            signature.update(plainText);
            return signature.sign();
        } catch (InvalidKeyException | SignatureException | NoSuchAlgorithmException e) {
            ((GeneralSecurityException)e).printStackTrace();
            return null;
        }
    }

    public static boolean verify(X509Certificate certificate, byte[] decodedText, byte[] receivedignature) {
        try {
            Signature signature = Signature.getInstance(certificate.getSigAlgName());
            signature.initVerify(certificate);
            signature.update(decodedText);
            return signature.verify(receivedignature);
        } catch (InvalidKeyException | SignatureException | NoSuchAlgorithmException e) {
            ((GeneralSecurityException)e).printStackTrace();
            return false;
        }
    }

    public static byte[] encode(byte[] plainText, PrivateKey privateKey) {
        try {
            Cipher cipher = Cipher.getInstance(privateKey.getAlgorithm());
            cipher.init(1, privateKey);
            return cipher.doFinal(plainText);
        } catch (NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException | NoSuchAlgorithmException e) {
            ((GeneralSecurityException)e).printStackTrace();
            return null;
        }
    }

    public static byte[] decode(byte[] encodedText, PublicKey publicKey) {
        try {
            Cipher cipher = Cipher.getInstance(publicKey.getAlgorithm());
            cipher.init(2, publicKey);
            return cipher.doFinal(encodedText);
        } catch (NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException | BadPaddingException | NoSuchAlgorithmException e) {
            ((GeneralSecurityException)e).printStackTrace();
            return null;
        }
    }
}

