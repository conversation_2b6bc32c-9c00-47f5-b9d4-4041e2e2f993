package com.hnbp.local.util;


import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.RandomAccessFile;

public class SplitRunnable implements Runnable {
    int byteSize;
    String partFileName;
    File originFile;
    int startPos;
    String partFilePath;

    public SplitRunnable(int byteSize, int startPos, String partFileName, File originFile, String partFilePath) {
        this.startPos = startPos;
        this.byteSize = byteSize;
        this.partFileName = partFileName;
        this.originFile = originFile;
        this.partFilePath = partFilePath;
    }

    public void run() {
        File dir = new File(this.partFilePath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try {
            RandomAccessFile rFile = new RandomAccessFile(this.originFile, "r");
            byte[] b = new byte[this.byteSize];
            rFile.seek((long)this.startPos);
            int s = rFile.read(b);
            OutputStream os = new FileOutputStream(this.partFilePath + File.separator + this.partFileName);
            os.write(b, 0, s);
            os.flush();
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}
