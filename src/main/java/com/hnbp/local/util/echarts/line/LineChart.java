package com.hnbp.local.util.echarts.line;

import java.util.List;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/5 17:08
 * @Title 折线统计图实体
 */
public class LineChart {
    private String title_text;//折线图标题
    private List legend_data;//折线图列说明
    private List xAxis_data;//折线图行说明
    private List<LineSeriesData> series;//折线图series 数据

    public String getTitle_text() {
        return title_text;
    }

    public void setTitle_text(String title_text) {
        this.title_text = title_text;
    }

    public List getLegend_data() {
        return legend_data;
    }

    public void setLegend_data(List legend_data) {
        this.legend_data = legend_data;
    }

    public List<LineSeriesData> getSeries() {
        return series;
    }

    public void setSeries(List<LineSeriesData> series) {
        this.series = series;
    }

    public List getxAxis_data() {
        return xAxis_data;
    }

    public void setxAxis_data(List xAxis_data) {
        this.xAxis_data = xAxis_data;
    }
}
