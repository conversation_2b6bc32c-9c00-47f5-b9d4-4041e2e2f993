package com.hnbp.local.util.echarts.pie;

import java.util.List;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/7/4 14:57
 * @Title 饼图参数
 */
public class PieChart {
    private String title_text;//饼图标题
    private List legend_data;//饼图图例说明
    private List<PieChartsSeriesData> series_data;//饼图series 数据

    public String getTitle_text() {
        return title_text;
    }

    public void setTitle_text(String title_text) {
        this.title_text = title_text;
    }

    public List getLegend_data() {
        return legend_data;
    }

    public void setLegend_data(List legend_data) {
        this.legend_data = legend_data;
    }

    public List<PieChartsSeriesData> getSeries_data() {
        return series_data;
    }

    public void setSeries_data(List<PieChartsSeriesData> series_data) {
        this.series_data = series_data;
    }
}
