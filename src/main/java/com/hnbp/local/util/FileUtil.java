package com.hnbp.local.util;


import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class FileUtil {
    public FileUtil() {
    }

    public static String leftPad(String str, int length, char ch) {
        if (str.length() >= length) {
            return str;
        } else {
            char[] chs = new char[length];
            Arrays.fill(chs, ch);
            char[] src = str.toCharArray();
            System.arraycopy(src, 0, chs, length - src.length, src.length);
            return new String(chs);
        }
    }

    public static boolean delete(String fileName) {
        boolean result = false;
        File f = new File(fileName);
        if (f.exists()) {
            result = f.delete();
        } else {
            result = true;
        }

        return result;
    }

    public static ArrayList<File> getAllFiles(String dirPath) {
        File dir = new File(dirPath);
        ArrayList<File> files = new ArrayList();
        if (dir.isDirectory()) {
            File[] fileArr = dir.listFiles();

            for(int i = 0; i < fileArr.length; ++i) {
                File f = fileArr[i];
                if (f.isFile()) {
                    files.add(f);
                } else {
                    files.addAll(getAllFiles(f.getPath()));
                }
            }
        }

        return files;
    }

    public static ArrayList<File> getDirFiles(String dirPath) {
        File path = new File(dirPath);
        File[] fileArr = path.listFiles();
        ArrayList<File> files = new ArrayList();

        for(File f : fileArr) {
            if (f.isFile()) {
                files.add(f);
            }
        }

        return files;
    }

    public static ArrayList<File> getDirFiles(String dirPath, final String suffix) {
        File path = new File(dirPath);
        File[] fileArr = path.listFiles(new FilenameFilter() {
            public boolean accept(File dir, String name) {
                String lowerName = name.toLowerCase();
                String lowerSuffix = suffix.toLowerCase();
                return lowerName.endsWith(lowerSuffix);
            }
        });
        ArrayList<File> files = new ArrayList();

        for(File f : fileArr) {
            if (f.isFile()) {
                files.add(f);
            }
        }

        return files;
    }

    public static String read(String fileName) throws IOException {
        File f = new File(fileName);
        FileInputStream fs = new FileInputStream(f);
        String result = null;
        byte[] b = new byte[fs.available()];
        fs.read(b);
        fs.close();
        result = new String(b);
        return result;
    }

    public static boolean write(String fileName, String fileContent) throws IOException {
        boolean result = false;
        File f = new File(fileName);
        FileOutputStream fs = new FileOutputStream(f);
        byte[] b = fileContent.getBytes();
        fs.write(b);
        fs.flush();
        fs.close();
        result = true;
        return result;
    }

    public static boolean append(String fileName, String fileContent) throws IOException {
        boolean result = false;
        File f = new File(fileName);
        if (f.exists()) {
            RandomAccessFile rFile = new RandomAccessFile(f, "rw");
            byte[] b = fileContent.getBytes();
            long originLen = f.length();
            rFile.setLength(originLen + (long)b.length);
            rFile.seek(originLen);
            rFile.write(b);
            rFile.close();
        }

        result = true;
        return result;
    }

    public List<String> splitBySize(String fileName, int byteSize) throws IOException {
        List<String> parts = new ArrayList();
        File file = new File(fileName);
        int count = (int)Math.ceil((double)file.length() / (double)byteSize);
        int countLen = (count + "").length();
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(count, count * 3, 1L, TimeUnit.SECONDS, new ArrayBlockingQueue(count * 2));

        for(int i = 0; i < count; ++i) {
            String partFileName = file.getName() + "." + leftPad(i + 1 + "", countLen, '0') + ".part";
            threadPool.execute(new SplitRunnable(byteSize, i * byteSize, partFileName, file));
            parts.add(partFileName);
        }

        return parts;
    }

    public void mergePartFiles(String dirPath, String partFileSuffix, int partFileSize, String mergeFileName) throws IOException {
        ArrayList<File> partFiles = getDirFiles(dirPath, partFileSuffix);
        Collections.sort(partFiles, new FileComparator());
        RandomAccessFile randomAccessFile = new RandomAccessFile(mergeFileName, "rw");
        randomAccessFile.setLength((long)(partFileSize * (partFiles.size() - 1)) + ((File)partFiles.get(partFiles.size() - 1)).length());
        randomAccessFile.close();
        ThreadPoolExecutor threadPool = new ThreadPoolExecutor(partFiles.size(), partFiles.size() * 3, 1L, TimeUnit.SECONDS, new ArrayBlockingQueue(partFiles.size() * 2));

        for(int i = 0; i < partFiles.size(); ++i) {
            threadPool.execute(new MergeRunnable((long)(i * partFileSize), mergeFileName, (File)partFiles.get(i)));
        }

    }

    private class FileComparator implements Comparator<File> {
        private FileComparator() {
        }

        public int compare(File o1, File o2) {
            return o1.getName().compareToIgnoreCase(o2.getName());
        }
    }

    private class SplitRunnable implements Runnable {
        int byteSize;
        String partFileName;
        File originFile;
        int startPos;

        public SplitRunnable(int byteSize, int startPos, String partFileName, File originFile) {
            this.startPos = startPos;
            this.byteSize = byteSize;
            this.partFileName = partFileName;
            this.originFile = originFile;
        }

        public void run() {
            try {
                RandomAccessFile rFile = new RandomAccessFile(this.originFile, "r");
                byte[] b = new byte[this.byteSize];
                rFile.seek((long)this.startPos);
                int s = rFile.read(b);
                OutputStream os = new FileOutputStream(this.partFileName);
                os.write(b, 0, s);
                os.flush();
                os.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }

    private class MergeRunnable implements Runnable {
        long startPos;
        String mergeFileName;
        File partFile;

        public MergeRunnable(long startPos, String mergeFileName, File partFile) {
            this.startPos = startPos;
            this.mergeFileName = mergeFileName;
            this.partFile = partFile;
        }

        public void run() {
            try {
                RandomAccessFile rFile = new RandomAccessFile(this.mergeFileName, "rw");
                rFile.seek(this.startPos);
                FileInputStream fs = new FileInputStream(this.partFile);
                byte[] b = new byte[fs.available()];
                fs.read(b);
                fs.close();
                rFile.write(b);
                rFile.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }
}
