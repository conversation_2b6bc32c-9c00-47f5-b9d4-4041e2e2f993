package com.hnbp.local.sftp.client;

import com.hnbp.local.sftp.model.Company;
import com.hnbp.local.sftp.model.Righttramsfer;
import com.hnbp.local.sftp.qt.DataInfoVo;
import com.hnbp.local.sftp.qt.DownloadResponseVo;
import com.hnbp.local.sftp.service.SftpService;
import com.hnbp.local.sftp.util.DataUtil;
import com.hnbp.local.sftp.util.RSAUtil;
import com.hnbp.local.sftp.util.SftpUtil;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.Charset;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SftpAction {
    @Autowired
    private SftpService sftpService;

    private static String fpublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDQWrxGOIr/757QGTIwnjcuqM7e" +
            "x6AASjiCPdRq5w79KPfoe5/tacV2GPxSXr7jo/svtKrc2+yiqs9kZHuOEHIo7AU39OWrOejLw+WX+tJvO5u/ZTh08YfikbPQEb4PYXV84" +
            "ceos0u3eXQxashEHApIDVL36UMdNl7WCTsc2knUwIDAQAB";

    private static String fgsKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDVQCp1PRh7wH1ela+/kX9vTVJjnWhTbuT2tE5td0AVlG" +
            "HpxZSaSLzy33Sd7bk32oSVAXOMp3IX2NUwUoIwCIgA12zfw3KUNXAyZ7FCNdVxHxTX/nQRGF3ecw3f74738mMMuoJ" +
            "oVefaL480MNcKo81/7fbzpDvNGs6B04R0Wf0RmQIDAQAB";

    private static String fbpPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMNBavEY4iv/vntA" +
            "ZMjCeNy6ozt7HoABKOII91GrnDv0o9+h7n+1pxXYY/FJevuOj+y+0qtzb7KKqz2R" +
            "ke44QcijsBTf05as56MvD5Zf60m87m79lOHTxh+KRs9ARvg9hdXzhx6izS7d5dDF" +
            "qyEQcCkgNUvfpQx02XtYJOxzaSdTAgMBAAECgYEAp50Js6w7w6+IEf/mRJ0E6d7h" +
            "71ayiQn10YwG2zopJSLOZlR5C3Kh21Pl/6SniTkJfh8VdA7lbOwzMeg70LbEaHsU" +
            "qp4uV6P/uQcnMc8KtjdFQsqsIX3Fq5aP4vFLy+NC7W3j/yqHiFOBvdase/FcFaJs" +
            "gb/rLu1EneU3adw9lpECQQDrhCnRKtjachbthP2IXUiYkZPBkEnZxxsqH28TWcOq" +
            "xt2AIMKv1Z33k1x4tCq52JMdkzzykb/OtjlxM99LrBNrAkEA1DzWf9PNddD3Btql" +
            "xetrCLngAhLfPGmuVNNkOqNY5g9N6+RdEz+0MmP567ZijTb9j6BCltUQhKYjrLfS" +
            "JJQduQJAJfpB5ADMxyzulY6Ec6DXPAL9mJDz17JLpxc6ihxHeRpoTWj6+++ucWXJ" +
            "24zx9tX9ox9DDLqD+4lW6fI7Z7Bv0QJAD3d/ikE9L+DSn8U/X3UQj8o78Oq/Kf/p" +
            "nHTrUPqmd92VqCM0RTrXPvTaObtI41GmmNjtAWD1mzUgVG3w+olwwQJAKIcnQgf0" +
            "SWXTvzCFdL8jgmuhqV5mU7vjyiCkwY4NQCclOSG5rmN6z3maz5GiTkKhVHsmKMQ9GAFXDDMQwYKIGw==";

    //定时执行(每个月的15号21:30运行一次)
    @Scheduled(cron = "0 30 21 15 * ?")
    public void downloadFile() throws SftpException, SQLException, UnsupportedEncodingException {
        String localPath = "D:/down20180313";

        DownloadResponseVo downloadResponseVo = new DownloadResponseVo();

        // ftp连接
        ChannelSftp sftp = null;

        DataUtil.createDir(localPath);
        try {
            sftp = SftpUtil.getSftpConnect("117.156.100.159", 20122, "shuiwuju", "AScdjw#(%&^3749");

        } catch (JSchException e) {
            downloadResponseVo.setCode(200);
            downloadResponseVo.setMsg("连接Sftp失败");
            e.printStackTrace();
        }
        Vector<LsEntry> vector = sftp.ls("/gongshangju");
        List<String> bmList = new ArrayList<String>();
        String wjm = "";
        if (vector.size() > 0) {
            for (LsEntry en : vector) {
                String filename = en.getFilename();
                if (!filename.equals(".") && !filename.equals("..")) {
                    bmList.add(filename);
                }
            }
        }
        List<String> insertlist1 = new ArrayList<String>();
        for (int i = 0; i < bmList.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("filename", bmList.get(i));
            sftpService.insertDzb(map);
//			 insertlist1.add("insert into dzb(filename)values('"+bmList.get(i)+"')");
        }
//		try {
//			jdbcUtil.executeBatch(insertlist1);
//		} catch (Exception e1) {
//			// TODO Auto-generated catch block
//			e1.printStackTrace();
//		}
//		String lb="select filename from dzb"
//				+ " MINUS"
//				+ " select filename from wjm";
//		ResultSet re=jdbcUtil.executeQuery(lb);
//		List<String>insertlist=new ArrayList<String>();
        List<Map<String, Object>> mapList = sftpService.findDzbWjm();
        List<Map<String, Object>> dzbWjm = getMapsToLower(mapList);
        Iterator<Map<String, Object>> iterator = dzbWjm.iterator();
        while (iterator.hasNext()) {
//			  String ftppath=re.getString(1);
            String ftppath = iterator.next().get("filename").toString();
            if (Integer.parseInt(ftppath.substring(0, 4)) < 2020) {
                continue;
            }
//            insertlist.add("insert into wjm(filename)values('" + ftppath + "')");
            Map<String, Object> map = new HashMap<>();
            map.put("filename", ftppath);
            sftpService.insertWjm(map);
            // 创建本地保存目录
            DataUtil.createDir(localPath);
            try {
                SftpUtil.downloadFile(ftppath, localPath, sftp);

            } catch (Exception e) {
                // 删除本地文件
                e.printStackTrace();
                downloadResponseVo.setCode(300);
                downloadResponseVo.setMsg("下载失败");
            }
            // 获取汇总文件信息
            File fileDir = new File(localPath);
            File[] fileList = fileDir.listFiles();
            String summaryFileName = null;
            Integer initNum = 0;
            for (File file : fileList) {
                String fileName = file.getName();
                if (fileName.contains("SUMMARY")) {

                    String tempSummaryFileName = fileName;
                    int end = tempSummaryFileName.indexOf(".");
                    int begin = tempSummaryFileName.lastIndexOf("-") + 1;
                    Integer tempNum = Integer.valueOf(tempSummaryFileName.substring(begin, end)).intValue();
                    if (tempNum > initNum) {
                        initNum = tempNum;
                        summaryFileName = fileName;
                    }
                }
            }
            List<DataInfoVo> dataFileFomatList = new ArrayList<DataInfoVo>();
            // 按行读取汇总文件
            List<String> lineList = DataUtil.readFileByLines(localPath + "/" + summaryFileName);
            if (lineList.size() > 0) {
                for (int i = 0; i < lineList.size(); i++) {
                    DataInfoVo dataFileFomatVo = new DataInfoVo();
                    String sjwjInfo = lineList.get(i);
                    String[] sjwj = sjwjInfo.split(",");
                    String typeName = sjwj[0];
                    String fileName = sjwj[1];
                    String signData = sjwj[3];
                    byte[] sign = Base64.decodeBase64(signData);
                    dataFileFomatVo.setTypeName(typeName);
                    try {
                        byte[] encodedData = DataUtil.readFileByte(localPath, fileName);
                        // 验证签名
                        if (RSAUtil.verify(encodedData, fgsKey, sign)) {
                            List<Object> list = new ArrayList<Object>();
                            // 解密
                            byte[] decodedData = RSAUtil.decryptByPrivateKey(encodedData, fbpPrivateKey);
                            String s = new String(decodedData);
                            BufferedReader br = new BufferedReader(
                                    new InputStreamReader(new ByteArrayInputStream(s.getBytes(Charset.forName("utf8"))),
                                            Charset.forName("utf8")));
                            String line;
                            while ((line = br.readLine()) != null) {
                                String json = new String(Base64.decodeBase64(line), "UTF-8");
                                //Object typeV = new Gson().fromJson(json, Object);
                                list.add(json);
                            }
                            dataFileFomatVo.setList(list);
                            dataFileFomatList.add(dataFileFomatVo);
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        downloadResponseVo.setCode(500);
                        downloadResponseVo.setMsg("解密失败");
                        //DataUtil.rmDir(localPath1);
                        //return downloadResponseVo;
                    }
                }
            }
            // 合并重复数据
            if (dataFileFomatList.size() > 0) {
                for (int i = 0; i < dataFileFomatList.size(); i++) {
                    DataInfoVo tempDataFileFomatVo = dataFileFomatList.get(i);
                    String tempTypeName = tempDataFileFomatVo.getTypeName();
                    List<Object> tempList = tempDataFileFomatVo.getList();
                    for (int j = i + 1; j < dataFileFomatList.size(); j++) {
                        DataInfoVo dataFileFomatVo = dataFileFomatList.get(j);
                        if (tempTypeName.equals(dataFileFomatVo.getTypeName())) {
                            List<Object> newList = dataFileFomatVo.getList();
                            dataFileFomatList.remove(j);
                            if (newList.size() > 0) {
                                for (Object object : newList) {
                                    tempList.add(object);
                                }
                            }
                            i--;
                            break;
                        }
                    }
                }
            }
            downloadResponseVo.setCode(100);
            downloadResponseVo.setData(dataFileFomatList);

            List<DataInfoVo> listdata = downloadResponseVo.getData();
            for (int i = 0; i < listdata.size(); i++) {
                DataInfoVo datzinfovo = listdata.get(i);
                if (datzinfovo.getTypeName().equalsIgnoreCase("COMPANY")) {
                    List<Object> object = datzinfovo.getList();
                    for (int j = 0; j < object.size(); j++) {
                        String json = object.get(j).toString();
                        JSONObject jsonObject = JSONObject.fromObject(json);
                        Company student = (Company) JSONObject.toBean(jsonObject, Company.class);
                        // Gson gson = new Gson();
                        // Company student = gson.fromJson(json, Company.class);
                        String bb = ftppath.substring(4, 6);
                        String cc = ftppath.substring(0, 4);
						student.setfSysYear(cc);
						student.setfSysMonth(bb);
						sftpService.insertCapitalMarket(student);
//                        String insert = "insert into m_t_domesticCapitalMarket"
//                                + "(fid,fsjjhwym,f_sys_year,f_sys_month,fztsfdm,fqymc,ftyshxydm,fzch,ftmc,fzt,fdjrq,ffddbfzr,fjjlx,fqylx,fzczb,userid,fcreator)"
//                                + "values"
//                                + "(newbpid('12345678'),'" + student.getUuid() + "','" + cc + "',"
//                                + "'" + bb + "','" + student.getPripid() + "'"
//                                + ",'" + student.getEntName() + "','" + student.getUniSCID() + "','" + student.getRegNO() + "'"
//                                + ",'" + student.getEntTypeName() + "','" + student.getEntState() + "','" + student.getEstDate() + "'"
//                                + ",'" + student.getLegal() + "','" + student.getEntTypeName() + "','" + student.getEntTypeName() + "',"
//                                + "'" + student.getRegCap() + "','b8cd5040e36549e18460b0875917a32d10000008','工商局')";
//                        insertlist.add(insert);

                    }
                } else if (datzinfovo.getTypeName().equalsIgnoreCase("RIGHTTRANSFER")) {
                    List<Object> object = datzinfovo.getList();
                    for (int j = 0; j < object.size(); j++) {
                        String json = object.get(j).toString();
                        JSONObject jsonObject = JSONObject.fromObject(json);
                        Righttramsfer student = (Righttramsfer) JSONObject.toBean(jsonObject, Righttramsfer.class);
                        //Gson gson = new Gson();
                        // Righttramsfer student = gson.fromJson(json, Righttramsfer.class);
                        String bb = ftppath.substring(4, 6);
                        String cc = ftppath.substring(0, 4);
                        student.setfSysYear(cc);
                        student.setfSysMonth(bb);
                        sftpService.insertTransferInfo(student);
//                        String insert = "insert into b_t_shareTransferInfo"
//                                + "(fid,fqymc,f_sys_year,f_sys_month,fqyshxxdm,fbgrq,fbgqgdqk,fsd,fbghgdqk,userid,fcreator)"
//                                + "values"
//                                + "(newbpid('12345678'),'" + student.getOldinv() + "','" + cc + "',"
//                                + "'" + bb + "','" + student.getUniSCID() + "'"
//                                + ",'" + student.getBelongDate() + "','" + student.getOldblictype_CN() + "',''"
//                                + ",'" + student.getNewblictype_CN() + "','b8cd5040e36549e18460b0875917a32d10000008','工商局')";
//                        insertlist.add(insert);
                    }
                }
            }
            File file = new File(localPath);
            deleteFile(file);
        }
        sftpService.deleteDzb();

//        insertlist.add("delete from dzb");
//        try {
//            jdbcUtil.executeBatch(insertlist);
//        } catch (Exception e) {
//            // TODO Auto-generated catch block
//            e.printStackTrace();
//        }


    }

    /**
     * list<Map>  key 转小写
     *
     * @param maps
     * @return
     */
    public List<Map<String, Object>> getMapsToLower(List<Map<String, Object>> maps) {
        return maps.parallelStream().map(it -> {
            Map<String, Object> map = new LinkedHashMap<>();
            Iterator iterator = it.keySet().iterator();
            while (iterator.hasNext()) {
                String next = (String) iterator.next();
                map.put(next.toLowerCase(), it.get(next));
            }
            return map;
        }).collect(Collectors.toList());
    }

    public static void deleteFile(File file) {
        //判断文件不为null或文件目录存在
        if (file == null || !file.exists()) {
            System.out.println("文件删除失败,请检查文件路径是否正确");
            return;
        }
        //取得这个目录下的所有子文件对象
        File[] files = file.listFiles();
        //遍历该目录下的文件对象
        for (File f : files) {
            //打印文件名
            String name = file.getName();
            System.out.println(name);
            //判断子目录是否存在子目录,如果是文件则删除
            if (f.isDirectory()) {
                deleteFile(f);
            } else {
                f.delete();
            }
        }
        //删除空文件夹  for循环已经把上一层节点的目录清空。
        file.delete();
    }

}
