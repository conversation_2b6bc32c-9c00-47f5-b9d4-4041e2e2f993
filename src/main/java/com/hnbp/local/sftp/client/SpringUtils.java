package com.hnbp.local.sftp.client;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * <AUTHOR> @version 1.0.0
 * @createTime 2021年07月06日 15:03:00
 */

public class SpringUtils implements ApplicationContextAware {
   static ApplicationContext ac = null;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        SpringUtils.ac=applicationContext;
    }

    public static <T> T getBean(String clz) throws BeansException {
        T result = (T) SpringUtils.ac.getBean(clz);
        return result;
    }
}
