package com.hnbp.local.sftp.model;

public class Righttramsfer {
    private String uuid;
    private String belongDate;
    private String uniSCID;
    private String regNO;
    private String pripid;
    private String oldinv;
    private String newinv;
    private String oldinvtype;
    private String newinvtype;
    private String oldinvtype_CN;
    private String newinvtype_CN;
    private String oldblictype;
    private String newblictype;
    private String oldblictype_CN;
    private String newblictype_CN;
    private String oldblicno;
    private String newblicno;
    private String oldsubconam;
    private String newsubconam;
    private String oldsubconform;
    private String newsubconform;
    private String altdate;
	private String fSysYear;
	private String fSysMonth;

	public String getfSysYear() {
		return fSysYear;
	}

	public void setfSysYear(String fSysYear) {
		this.fSysYear = fSysYear;
	}

	public String getfSysMonth() {
		return fSysMonth;
	}

	public void setfSysMonth(String fSysMonth) {
		this.fSysMonth = fSysMonth;
	}

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getBelongDate() {
        return belongDate;
    }

    public void setBelongDate(String belongDate) {
        this.belongDate = belongDate;
    }

    public String getUniSCID() {
        return uniSCID;
    }

    public void setUniSCID(String uniSCID) {
        this.uniSCID = uniSCID;
    }

    public String getRegNO() {
        return regNO;
    }

    public void setRegNO(String regNO) {
        this.regNO = regNO;
    }

    public String getPripid() {
        return pripid;
    }

    public void setPripid(String pripid) {
        this.pripid = pripid;
    }

    public String getOldinv() {
        return oldinv;
    }

    public void setOldinv(String oldinv) {
        this.oldinv = oldinv;
    }

    public String getNewinv() {
        return newinv;
    }

    public void setNewinv(String newinv) {
        this.newinv = newinv;
    }

    public String getOldinvtype() {
        return oldinvtype;
    }

    public void setOldinvtype(String oldinvtype) {
        this.oldinvtype = oldinvtype;
    }

    public String getNewinvtype() {
        return newinvtype;
    }

    public void setNewinvtype(String newinvtype) {
        this.newinvtype = newinvtype;
    }

    public String getOldinvtype_CN() {
        return oldinvtype_CN;
    }

    public void setOldinvtype_CN(String oldinvtype_CN) {
        this.oldinvtype_CN = oldinvtype_CN;
    }

    public String getNewinvtype_CN() {
        return newinvtype_CN;
    }

    public void setNewinvtype_CN(String newinvtype_CN) {
        this.newinvtype_CN = newinvtype_CN;
    }

    public String getOldblictype() {
        return oldblictype;
    }

    public void setOldblictype(String oldblictype) {
        this.oldblictype = oldblictype;
    }

    public String getNewblictype() {
        return newblictype;
    }

    public void setNewblictype(String newblictype) {
        this.newblictype = newblictype;
    }

    public String getOldblictype_CN() {
        return oldblictype_CN;
    }

    public void setOldblictype_CN(String oldblictype_CN) {
        this.oldblictype_CN = oldblictype_CN;
    }

    public String getNewblictype_CN() {
        return newblictype_CN;
    }

    public void setNewblictype_CN(String newblictype_CN) {
        this.newblictype_CN = newblictype_CN;
    }

    public String getOldblicno() {
        return oldblicno;
    }

    public void setOldblicno(String oldblicno) {
        this.oldblicno = oldblicno;
    }

    public String getNewblicno() {
        return newblicno;
    }

    public void setNewblicno(String newblicno) {
        this.newblicno = newblicno;
    }

    public String getOldsubconam() {
        return oldsubconam;
    }

    public void setOldsubconam(String oldsubconam) {
        this.oldsubconam = oldsubconam;
    }

    public String getNewsubconam() {
        return newsubconam;
    }

    public void setNewsubconam(String newsubconam) {
        this.newsubconam = newsubconam;
    }

    public String getOldsubconform() {
        return oldsubconform;
    }

    public void setOldsubconform(String oldsubconform) {
        this.oldsubconform = oldsubconform;
    }

    public String getNewsubconform() {
        return newsubconform;
    }

    public void setNewsubconform(String newsubconform) {
        this.newsubconform = newsubconform;
    }

    public String getAltdate() {
        return altdate;
    }

    public void setAltdate(String altdate) {
        this.altdate = altdate;
    }

}
