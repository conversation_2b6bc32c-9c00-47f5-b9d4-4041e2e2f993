package com.hnbp.local.sftp.service;


import com.hnbp.local.sftp.model.Company;
import com.hnbp.local.sftp.model.Righttramsfer;

import java.util.List;
import java.util.Map;


public interface SftpService {

    int insertDzb(Map<String, Object> parameterMap);

    List<Map<String, Object>> findDzbWjm();

    int insertWjm(Map<String, Object> parameterMap);

    int insertCapitalMarket(Company company);

    int insertTransferInfo(Righttramsfer righttramsfer);

    int deleteDzb();
}
