package com.hnbp.local.sftp.service;

import com.hnbp.local.sftp.mapper.SftpMapper;
import com.hnbp.local.sftp.model.Company;
import com.hnbp.local.sftp.model.Righttramsfer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Service
public class SftpServiceImpl implements SftpService {

    @Autowired
    private SftpMapper sftpMapper;

    @Override
    public int insertDzb(Map<String, Object> parameterMap) {
        return sftpMapper.insertDzb(parameterMap);
    }

    @Override
    public List<Map<String, Object>> findDzbWjm() {
        return sftpMapper.findDzbWjm();
    }

    @Override
    public int insertWjm(Map<String, Object> parameterMap) {
        return sftpMapper.insertWjm(parameterMap);
    }

    @Override
    public int insertCapitalMarket(Company company) {
        return sftpMapper.insertCapitalMarket(company);
    }

    @Override
    public int insertTransferInfo(Righttramsfer righttramsfer) {
        return sftpMapper.insertTransferInfo(righttramsfer);
    }

    @Override
    public int deleteDzb() {
        return sftpMapper.deleteDzb();
    }


}
