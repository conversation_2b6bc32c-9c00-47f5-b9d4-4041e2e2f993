package com.hnbp.local.sftp.util;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月5日
 *
 * 版权所有(C) 2011-2018。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public class DataUtil {

	private static String sepa = File.separator;

	/**
	 * 数据分组(每1000条)
	 * 
	 * @param dataList
	 * @return
	 */
	public static List<List<Object>> getList(List<Object> dataList) {
		if ( dataList.size() > 0) {
			int sjwjCount = dataList.size() / 1000;
			if (dataList.size() % 1000 > 0) {
				sjwjCount = sjwjCount + 1;
			}
			List<List<Object>> listSerial = new ArrayList<List<Object>>();
			if (sjwjCount > 0) {
				for (int i = 0; i < sjwjCount; i++) {
					List<Object> list = new ArrayList<Object>();
					for (int j = i * 1000; j < dataList.size() && j < (i * 1000 + 1000); j++) {
						list.add(dataList.get(j));
					}
					listSerial.add(list);
				}
			}
			return listSerial;
		}
		return null;
	}

	/**
	 * 写文件
	 * 
	 * @param filePath
	 * @param fileName
	 * @param data
	 * @param append
	 *            是否追加
	 * @return
	 */
	public static boolean writeFile(String filePath, String fileName, String data, boolean append) {
		try {
			File file = new File(filePath + sepa + fileName);
			if (!file.exists()) {
				file.createNewFile();
			}
			FileWriter fw = new FileWriter(file, append);
			BufferedWriter bw = new BufferedWriter(fw);
			bw.write(data);
			bw.flush();
			bw.close();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * 写文件
	 * 
	 * @param dstPath
	 * @param data
	 */
	public static void writeFileByte(String dstPath, String fileName, byte[] data, boolean append) {

		File dstFile = new File(dstPath + sepa + fileName);
		FileOutputStream out = null;
		try {
			out = new FileOutputStream(dstPath + sepa + fileName, append);
			out.write(data);
			if (append) {
				out.write("\r\n".getBytes());
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (out != null) {
				try {
					out.close();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 读文件
	 * 
	 * @param filename
	 * @return
	 * @throws IOException
	 */
	public static byte[] readFileByte(String filePath, String fileName) throws IOException {

		File f = new File(filePath + "\\" + fileName);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath + "\\" + fileName);
		}
		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			bos.close();
		}
	}

	/**
	 * 按行读取文件
	 * 
	 * @param fileName
	 */
	public static List<String> readFileByLines(String fileName) {
		File file = new File(fileName);
		BufferedReader reader = null;
		List<String> list = new ArrayList<String>();
		try {
			reader = new BufferedReader(new FileReader(file));
			String tempString = null;
			int line = 1;
			while ((tempString = reader.readLine()) != null) {
				list.add(tempString);
				line++;
			}
			reader.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e1) {
				}
			}
		}
		return list;
	}

	/**
	 * 数值转换
	 * 
	 * @param temp
	 * @return
	 */
	public static String getAppendString(int temp) {
		String code = temp < 1000 ? (temp < 10 ? ("00" + temp) : (temp < 100 ? "0" + temp : "" + temp)) : "1000";
		return code;
	}

	/**
	 * 创建文件夹
	 * 
	 * @param destDirName
	 * @return
	 */
	public static boolean createDir(String destDirName) {
		File dir = new File(destDirName);
		if (dir.exists()) {
			return false;
		}
		if (!destDirName.endsWith(File.separator)) {
			destDirName = destDirName + File.separator;
		}
		if (dir.mkdirs()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 获取文件夹下所有文件名称
	 * 
	 * @param strPath
	 * @return
	 */
	public static List<String> getFileList(String strPath) {
		File dir = new File(strPath);
		File[] files = dir.listFiles(); // 该文件目录下文件全部放入数组
		List<String> filelist = new ArrayList<String>();
		if (files != null) {
			for (int i = 0; i < files.length; i++) {
				String fileName = files[i].getName();
				if (files[i].isDirectory()) {
					getFileList(files[i].getName());
				} else {
					String strFileName = files[i].getName();
					filelist.add(strFileName);
					continue;
				}
			}
		}
		return filelist;
	}

	/**
	 * 删除本地路径下的所有文件
	 * 
	 * @param path
	 */
	public static boolean rmDir(String dir) {
		if (!dir.endsWith(File.separator))
			dir = dir + File.separator;
		File dirFile = new File(dir);
		if ((!dirFile.exists()) || (!dirFile.isDirectory())) {
			return false;
		}
		boolean flag = true;
		File[] files = dirFile.listFiles();
		for (int i = 0; i < files.length; i++) {
			if (files[i].isFile()) {
				flag = deleteFile(files[i].getAbsolutePath());
				if (!flag)
					break;
			} else if (files[i].isDirectory()) {
				flag = deleteDirectory(files[i].getAbsolutePath());
				if (!flag)
					break;
			}
		}
		if (!flag) {
			System.out.println("删除目录失败！");
			return false;
		}
		if (dirFile.delete()) {
			System.out.println("删除目录" + dir + "成功！");
			return true;
		} else {
			return false;
		}
	}

	public static boolean deleteFile(String fileName) {
		File file = new File(fileName);
		if (file.exists() && file.isFile()) {
			if (file.delete()) {
				return true;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	public static boolean deleteDirectory(String dir) {
		if (!dir.endsWith(File.separator))
			dir = dir + File.separator;
		File dirFile = new File(dir);
		if ((!dirFile.exists()) || (!dirFile.isDirectory())) {
			return false;
		}
		boolean flag = true;
		File[] files = dirFile.listFiles();
		for (int i = 0; i < files.length; i++) {
			if (files[i].isFile()) {
				flag = deleteFile(files[i].getAbsolutePath());
				if (!flag)
					break;
			}
			// 删除子目录
			else if (files[i].isDirectory()) {
				flag = deleteDirectory(files[i].getAbsolutePath());
				if (!flag)
					break;
			}
		}
		if (!flag) {
			return false;
		}
		// 删除当前目录
		if (dirFile.delete()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 拆分文件个数
	 * 
	 * @param fileName
	 *            待拆分的完整文件名
	 * @param dirName
	 *            保存目录名
	 * @param byteSize
	 *            按多少字节大小拆分
	 * @return
	 */
	public static int splitBySize(String fileName, String dirName, int byteSize) {
		File file = new File(dirName + fileName);
		int count = 0;
		if (file.exists()) {
			count = (int) Math.ceil(file.length() / (double) byteSize);
		}
		return count;
	}

}
