package com.hnbp.local.sftp.util;

import java.io.Serializable;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月3日
 *
 * 版权所有(C) 2011-2018。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public class ConnectionVo implements Serializable {

	private static final long serialVersionUID = -8024635157686157963L;
	private String host;
	private Integer port;
	private String user;
	private String pswd;
	private String fileUpLoadPath;
	private String fileDownLoadPath;
	private String fileSplitPath;

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	public String getUser() {
		return user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public String getPswd() {
		return pswd;
	}

	public void setPswd(String pswd) {
		this.pswd = pswd;
	}

	public String getFileUpLoadPath() {
		return fileUpLoadPath;
	}

	public void setFileUpLoadPath(String fileUpLoadPath) {
		this.fileUpLoadPath = fileUpLoadPath;
	}

	public String getFileDownLoadPath() {
		return fileDownLoadPath;
	}

	public void setFileDownLoadPath(String fileDownLoadPath) {
		this.fileDownLoadPath = fileDownLoadPath;
	}

	public String getFileSplitPath() {
		return fileSplitPath;
	}

	public void setFileSplitPath(String fileSplitPath) {
		this.fileSplitPath = fileSplitPath;
	}

}
