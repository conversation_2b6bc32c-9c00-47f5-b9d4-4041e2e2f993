package com.hnbp.local.sftp.util;

import com.hnbp.local.util.CertifacateUtil;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.pkcs.RSAPrivateKeyStructure;

import java.io.IOException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.X509Certificate;
import java.security.spec.RSAPrivateKeySpec;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月9日
 *
 * 版权所有(C) 2011-2018。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public class KeyConfig {

	private static String privateKeyPath;
	private static String privateKeyName;
	private static String certPath;
	private static String certName;

	/**
	 * 获取公钥
	 * 
	 * @return
	 * @throws IOException
	 */
	public static String getPublicKey() throws IOException {
		X509Certificate receivedCertificate;
		receivedCertificate = CertifacateUtil.getCertificateByCertPath(certPath + certName);
		PublicKey publicKey = CertifacateUtil.getPublicKey(receivedCertificate);
		return Base64.encodeBase64String(publicKey.getEncoded());
	}

	/**
	 * 获取私钥
	 * 
	 * @return
	 * @throws Exception
	 */
	public static String getPrivateKeyString() throws Exception {
		return Base64.encodeBase64String(getPrivateKey().getEncoded());
	}

	/**
	 * 获取私钥
	 * 
	 * @return
	 * @throws Exception
	 */
	public static PrivateKey getPrivateKey() throws Exception {
		byte[] text = Base64.decodeBase64(DataUtil.readFileByte(privateKeyPath, privateKeyName));
		@SuppressWarnings("deprecation")
		RSAPrivateKeyStructure asn1PrivKey = new RSAPrivateKeyStructure(
				(ASN1Sequence) ASN1Sequence.fromByteArray(text));
		@SuppressWarnings("deprecation")
		RSAPrivateKeySpec rsaPrivKeySpec = new RSAPrivateKeySpec(asn1PrivKey.getModulus(),
				asn1PrivKey.getPrivateExponent());
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		PrivateKey priKey = keyFactory.generatePrivate(rsaPrivKeySpec);
		return priKey;
	}
	/**
	 * 获取公钥
	 * 
	 * @return
	 * @throws Exception
	 */
/*	public static PrivateKey getPuivateKey() throws Exception {
		byte[] text = Base64.decodeBase64(DataUtil.readFileByte(privateKeyPath, privateKeyName));
		@SuppressWarnings("deprecation")
		RSAPrivateKeyStructure asn1PrivKey = new RSAPrivateKeyStructure(
				(ASN1Sequence) ASN1Sequence.fromByteArray(text));
		@SuppressWarnings("deprecation")
		RSAPrivateKeySpec rsaPrivKeySpec = new RSAPrivateKeySpec(asn1PrivKey.getModulus(),
				asn1PrivKey.getPrivateExponent());
		KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		PrivateKey priKey = keyFactory.generatePrivate(rsaPrivKeySpec);
		return priKey;
	}*/
	/**
	 * 私钥签名
	 * 
	 * @param privateKey
	 * @param plainText
	 * @return
	 * @throws Exception
	 */
	public static byte[] sign(PrivateKey privateKey, byte[] plainText) throws Exception {
		X509Certificate certificate = CertifacateUtil.getCertificateByCertPath("C:/key/" + "pi.key");
		return CertifacateUtil.sign(certificate, privateKey, plainText);
	}

	/**
	 * 验签
	 * 
	 * @param decodedText
	 * @param receivedignature
	 * @return
	 */
	public static boolean verify(byte[] decodedText, byte[] receivedignature) {
		X509Certificate certificate;
		try {
			certificate = CertifacateUtil.getCertificateByCertPath("C:/key/" + "pu.key");
			return CertifacateUtil.verify(certificate, decodedText, receivedignature);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return false;

	}

	public String getPrivateKeyPath() {
		return privateKeyPath;
	}

	public void setPrivateKeyPath(String privateKeyPath) {
		this.privateKeyPath = privateKeyPath;
	}

	public String getPrivateKeyName() {
		return privateKeyName;
	}

	public void setPrivateKeyName(String privateKeyName) {
		this.privateKeyName = privateKeyName;
	}

	public String getCertPath() {
		return certPath;
	}

	public void setCertPath(String certPath) {
		this.certPath = certPath;
	}

	public String getCertName() {
		return certName;
	}

	public void setCertName(String certName) {
		this.certName = certName;
	}

}
