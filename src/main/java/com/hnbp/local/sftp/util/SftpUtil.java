package com.hnbp.local.sftp.util;

import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import org.apache.commons.lang.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;

public class SftpUtil {

	private static final String UPLOAD_PRE_FIX = "/shuiwuju/";
	static  ChannelSftp sftp = null;
	private static final String DOWNLOAD_PRE_FIX = "/gongshangju/";
	private static String sepa = File.separator;
	/**
	 * sftp连接池.
	 */
	private static final Map<String, Channel> SFTP_CHANNEL_POOL = new HashMap<String, Channel>();

	/**
	 * 获取sftp协议连接.
	 * 
	 * @param host
	 *            主机名
	 * @param port
	 *            端口
	 * @param username
	 *            用户名
	 * @param password
	 *            密码
	 * @return 连接对象
	 * @throws JSchException
	 *             异常
	 */
	public static synchronized  ChannelSftp getSftpConnect(final String host, final int port, final String username,
			final String password) throws JSchException {
		if(sftp!=null){
			return sftp;
		}
		Session sshSession = null;
		Channel channel = null;
		ChannelSftp sftp = null;
		String key = "***************" + "," + "20122" + "," + "shuiwuju" + "," + "AScdjw#(%&^3749";
		if (null == SFTP_CHANNEL_POOL.get(key)) {
			JSch jsch = new JSch();
			jsch.getSession(username, host, port);
			sshSession = jsch.getSession(username, host, port);
			sshSession.setPassword(password);
			Properties sshConfig = new Properties();
			sshConfig.put("StrictHostKeyChecking", "no");
			sshSession.setConfig(sshConfig);
			sshSession.connect();
			channel = sshSession.openChannel("sftp");
			channel.connect();
			SFTP_CHANNEL_POOL.put(key, channel);
		} else {
			channel = SFTP_CHANNEL_POOL.get(key);
			sshSession = channel.getSession();
			if (!sshSession.isConnected()) {
				sshSession.connect();
			}
			if (!channel.isConnected()) {
				channel.connect();
			}
		}
		sftp = (ChannelSftp) channel;
		return sftp;
	}

	/**
	 * 下载文件-sftp协议.
	 * 
	 * @param downloadFile
	 *            下载的文件
	 * @param saveFile
	 *            存在本地的路径
	 * @param sftp
	 *            sftp连接
	 * @return 文件
	 * @throws Exception
	 *             异常
	 */
	public static File download(final String downloadFile, final String saveFile, final ChannelSftp sftp)
			throws Exception {
		FileOutputStream os = null;
		File file = new File(saveFile);
		try {
			if (!file.exists()) {
				File parentFile = file.getParentFile();
				if (!parentFile.exists()) {
					parentFile.mkdirs();
				}
				file.createNewFile();
			}
			os = new FileOutputStream(file);
			List<String> list = formatPath(downloadFile);
			sftp.get(list.get(0) + list.get(1), os);
		} catch (Exception e) {
			throw e;
		} finally {
			os.close();
		}
		return file;
	}

	/**
	 * 下载文件-sftp协议.
	 * 
	 * @param downloadFile
	 *            下载的文件
	 * @param saveFile
	 *            存在本地的路径
	 * @param sftp
	 *            sftp连接
	 * @return 文件 byte[]
	 * @throws Exception
	 *             异常
	 */
	public static byte[] downloadAsByte(final String downloadFile, final ChannelSftp sftp) throws Exception {
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		try {
			List<String> list = formatPath(downloadFile);
			sftp.get(list.get(0) + list.get(1), os);
		} catch (Exception e) {
			throw e;
		} finally {
			os.close();
		}
		return os.toByteArray();
	}

	/**
	 * 删除文件-sftp协议.
	 * 
	 * @param pathString
	 *            要删除的文件
	 * @param sftp
	 *            sftp连接
	 * @throws SftpException
	 *             异常
	 */
	public static void rmFile(final String pathString, final ChannelSftp sftp) throws SftpException {
		if (dirExist(UPLOAD_PRE_FIX + pathString, sftp)) {
			sftp.rm(UPLOAD_PRE_FIX + pathString);
		}
	}

	/**
	 * 删除文件夹-sftp协议.如果文件夹有内容，则会抛出异常.
	 * 
	 * @param pathString
	 *            文件夹路径
	 * @param sftp
	 *            sftp连接
	 * @param resursion
	 *            递归删除
	 * @throws SftpException
	 *             异常
	 */
	public static void rmDir(final String pathString, final ChannelSftp sftp, final boolean recursion)
			throws SftpException {
		if (dirExist(UPLOAD_PRE_FIX + pathString, sftp)) {
			if (recursion) {
				removeFile(pathString, sftp);
			} else {
				sftp.rmdir(pathString);
			}
		}
	}

	public static void removeFile(String pathString, final ChannelSftp sftp) throws SftpException {
		Vector<LsEntry> vector = sftp.ls(UPLOAD_PRE_FIX + pathString);
		sftp.cd(UPLOAD_PRE_FIX + pathString);
		if (vector.size() > 1) {
			String fileName = "";
			for (LsEntry en : vector) {
				fileName = en.getFilename();
				if (".".equals(fileName) || "..".equals(fileName)) {
					continue;
				} else {
					sftp.rm(fileName);
				}
			}
		}
	}

	/**
	 * 递归删除执行.
	 * 
	 * @param pathString
	 *            文件路径
	 * @param sftp
	 *            sftp连接
	 * @throws SftpException
	 */
	private static void exeRmRec(final String pathString, final ChannelSftp sftp) throws SftpException {
		@SuppressWarnings("unchecked")
		Vector<LsEntry> vector = sftp.ls(pathString);
		if (vector.size() == 1) { // 文件，直接删除
			sftp.rm(pathString);
		} else if (vector.size() == 2) { // 空文件夹，直接删除
			sftp.rmdir(pathString);
		} else {
			String fileName = "";
			// 删除文件夹下所有文件
			for (LsEntry en : vector) {
				fileName = en.getFilename();
				if (".".equals(fileName) || "..".equals(fileName)) {
					continue;
				} else {
					exeRmRec(pathString + sepa + fileName, sftp);
				}
			}
			// 删除文件夹
			sftp.rmdir(pathString);
		}
	}

	/**
	 * 上传文件-sftp协议.
	 * 
	 * @param srcFile
	 *            源文件
	 * @param dir
	 *            保存路径
	 * @param fileName
	 *            保存文件名
	 * @param sftp
	 *            sftp连接
	 * @throws Exception
	 *             异常
	 */
	private static void uploadFileSftp(final String srcFile, final String dir, final String fileName,
			final ChannelSftp sftp) throws SftpException {
		sftp.cd(UPLOAD_PRE_FIX);
		if (!dirExist(dir, sftp)) {
			sftp.mkdir(dir);
		}
		sftp.cd(dir);
		sftp.put(srcFile, fileName);
	}

	/**
	 * 上传文件
	 * 
	 * @param srcFile
	 *            源文件
	 * @param fileName
	 *            文件名
	 * @param dir
	 *            保存路径
	 * @param sftp
	 * @return
	 * @throws SftpException
	 */
	public static boolean uploadFile(String srcFile, String fileName, String dir, final ChannelSftp sftp)
			throws SftpException {
		File file = new File(srcFile + sepa + fileName);
		if (file.exists()) {
			// List<String> list = formatPath(srcFile, dir);
			uploadFileSftp(srcFile + sepa + fileName, dir, fileName, sftp);
			return true;
		}
		return false;
	}

	public static boolean uploadSftp(String localPath, String fileName, String shortDir, ChannelSftp sftp)
			throws SftpException {
		File file = new File(localPath + sepa + fileName);
		if (file.exists()) {
			uploadFileSftp(localPath + sepa + fileName, UPLOAD_PRE_FIX + shortDir, fileName, sftp);
			return true;
		}
		return false;
	}

	/**
	 * 根据路径创建文件夹.
	 * 
	 * @param dir
	 *            路径 必须是 /xxx/xxx/ 不能就单独一个/
	 * @param sftp
	 *            sftp连接
	 * @throws SftpException
	 *             异常
	 */
	public static boolean mkdir(final String dir, final ChannelSftp sftp) throws SftpException {
		if (StringUtils.isBlank(dir)) {
			return false;
		}
		String md = dir.replaceAll("\\\\", "/");
		if (md.indexOf("/") != 0 || md.length() == 1) {
			return false;
		}
		return mkdirs(md, sftp);
	}

	/**
	 * 递归创建文件夹.
	 * 
	 * @param dir
	 *            路径
	 * @param sftp
	 *            sftp连接
	 * @return 是否创建成功
	 * @throws SftpException
	 *             异常
	 */
	private static boolean mkdirs(final String dir, final ChannelSftp sftp) throws SftpException {
		String dirs = dir.substring(1, dir.length() - 1);
		String[] dirArr = dirs.split("/");
		String base = "";
		for (String d : dirArr) {
			base += "/" + d;
			if (dirExist(base + "/", sftp)) {
				continue;
			} else {
				sftp.mkdir(base + "/");
			}
		}
		return true;
	}

	/**
	 * 判断文件夹是否存在.
	 * 
	 * @param dir
	 *            文件夹路径， /xxx/xxx/
	 * @param sftp
	 *            sftp协议
	 * @return 是否存在
	 */
	private static boolean dirExist(final String dir, final ChannelSftp sftp) {
		try {
			Vector<?> vector = sftp.ls(dir);
			if (null == vector) {
				return false;
			} else {
				return true;
			}
		} catch (SftpException e) {
			return false;
		}
	}

	/**
	 * 格式化路径.
	 * 
	 * @param srcPath
	 *            原路径. /xxx/xxx/xxx.yyy 或 X:/xxx/xxx/xxx.yy
	 * @return list, 第一个是路径（/xxx/xxx/）,第二个是文件名（xxx.yy）
	 */
	public static List<String> formatPath(final String srcPath) {
		List<String> list = new ArrayList<String>(2);
		String repSrc = srcPath.replaceAll("\\\\", "/");
		int firstP = repSrc.indexOf("/");
		int lastP = repSrc.lastIndexOf("/");
		String fileName = lastP + 1 == repSrc.length() ? "" : repSrc.substring(lastP + 1);
		String dir = firstP == -1 ? "" : repSrc.substring(firstP, lastP);
		dir = (dir.length() == 1 ? dir : (dir + "/"));
		list.add(dir);
		list.add(fileName);
		return list;
	}

	public static List<String> formatPath(final String srcPath, String dirFtp) {
		List<String> list = new ArrayList<String>(2);
		String repSrc = srcPath.replaceAll("\\\\", "/");
		int firstP = repSrc.indexOf("/");
		int lastP = repSrc.lastIndexOf("/");
		String fileName = lastP + 1 == repSrc.length() ? "" : repSrc.substring(lastP + 1);
		String dir = firstP == -1 ? "" : repSrc.substring(firstP, lastP);
		dir = UPLOAD_PRE_FIX + dirFtp;
		list.add(dir);
		list.add(fileName);
		return list;
	}

	/**
	 * 关闭协议-sftp协议.(关闭会导致连接池异常，因此不建议用户自定义关闭)
	 * 
	 * @param sftp
	 *            sftp连接
	 */
	private static void exit(final ChannelSftp sftp) {
		sftp.exit();
	}

	/**
	 * 获取sftp文件下所有文件
	 * 
	 * @param pathString
	 *            文件路径
	 * @param sftp
	 *            sftp连接
	 * @throws SftpException
	 */
	private static List<String> getRmRec(final String pathString, final ChannelSftp sftp) throws SftpException {
		@SuppressWarnings("unchecked")
		Vector<LsEntry> vector = sftp.ls(pathString);
		List<String> fileList = new ArrayList<String>();
		if (vector.size() > 0) {
			for (LsEntry en : vector) {
				if (!en.getAttrs().isDir()) {
					fileList.add(en.getFilename());
				}
			}
		}
		return fileList;
	}

	/**
	 * 下载文件-sftp协议.
	 * 
	 * @param dirName
	 *            下载的文件目录
	 * @param saveFile
	 *            存在本地的路径
	 * @param sftp
	 *            sftp连接
	 * @return 文件
	 * @throws Exception
	 *             异常
	 */
	public static void downloadFile(String dirName, String saveFile, ChannelSftp sftp) throws Exception {

		FileOutputStream os = null;
		sftp.cd(DOWNLOAD_PRE_FIX);
		if (dirExist(dirName, sftp)) {
			sftp.cd(DOWNLOAD_PRE_FIX + dirName);
			List<String> fileList = getRmRec(DOWNLOAD_PRE_FIX + dirName, sftp);
			if (fileList.size() > 0) {

				for (String src : fileList) {
					File file = new File(saveFile, src);
					os = new FileOutputStream(file);
					sftp.get(src, os);
					os.close();
				}
			}
		}
	}
	
}