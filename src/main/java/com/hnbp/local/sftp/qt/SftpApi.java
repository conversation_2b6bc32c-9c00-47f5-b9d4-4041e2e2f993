package com.hnbp.local.sftp.qt;

import java.io.File;
import java.util.List;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月3日
 *
 * 版权所有(C) 2011-2017。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public interface SftpApi {

	/**
	 * 上传 加密
	 * 
	 * @param dataList
	 * @param serialNumberSum
	 * @return
	 * @throws Exception
	 */
	public UploadResponseVo uploadFile(List<DataInfoVo> dataList, String serialNumberSum) throws Exception;

	/**
	 * 下载 解密
	 * 
	 * @param date
	 * @return
	 */
	public DownloadResponseVo downloadFile(String date);

	/**
	 * 文件分片
	 * 
	 * @param fileName
	 *            数据文件名
	 * @param dirName
	 *            数据文件保存目录名
	 * @return
	 */
	public List<String> splitFile(String fileName, String dirName);

	/**
	 * 下载分片文件
	 * 
	 * @param splitFileName
	 *            分片文件名
	 * @param dirName
	 *            数据文件保存目录名
	 * @return
	 */
	public File downloadFileSplit(String splitFileName, String dirName);
}
