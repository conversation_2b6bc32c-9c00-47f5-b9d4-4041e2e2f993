package com.hnbp.local.sftp.qt;

import java.io.Serializable;
import java.util.List;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月11日
 *
 * 版权所有(C) 2011-2017。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */

/*上传返回格式*/
public class UploadResponseVo implements Serializable {

	private static final long serialVersionUID = -5802982494632994324L;

	/* 状态码 */
	private Integer code;
	/* 错误信息 */
	private String msg;
	/* 数据信息 */
	private List<FileInfoVo> data;

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<FileInfoVo> getData() {
		return data;
	}

	public void setData(List<FileInfoVo> data) {
		this.data = data;
	}

}
