package com.hnbp.local.sftp.qt;

import java.io.Serializable;
import java.util.List;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月10日
 *
 * 版权所有(C) 2011-2017。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public class DataInfoVo implements Serializable{

	private static final long serialVersionUID = -8873704712250529967L;
	
	private String typeName;
	private List<Object> list;
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public List<Object> getList() {
		return list;
	}
	public void setList(List<Object> list) {
		this.list = list;
	}

}
