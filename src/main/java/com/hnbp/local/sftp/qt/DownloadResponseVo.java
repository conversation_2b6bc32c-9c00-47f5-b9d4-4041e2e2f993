package com.hnbp.local.sftp.qt;

import java.io.Serializable;
import java.util.List;

public class DownloadResponseVo implements Serializable {

	private static final long serialVersionUID = 1038473152539855883L;

	/* 状态码 */
	private Integer code;
	/* 错误信息 */
	private String msg;
	/* 数据信息 */
	private List<DataInfoVo> data;

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<DataInfoVo> getData() {
		return data;
	}

	public void setData(List<DataInfoVo> data) {
		this.data = data;
	}

}
