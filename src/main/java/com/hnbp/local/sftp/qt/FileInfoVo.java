package com.hnbp.local.sftp.qt;

import java.io.Serializable;

/*
 * 创建者：杨文静
 * 创建日期：2018年1月10日
 *
 * 版权所有(C) 2011-2017。北京学佳澳教育科技有限公司。
 * 保留所有权利。
 */
public class FileInfoVo implements Serializable {

	private static final long serialVersionUID = 7996240869727129623L;

	private String typeName;
	private String fileName;
	private String summaryName;
	private Integer dataCount;

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getSummaryName() {
		return summaryName;
	}

	public void setSummaryName(String summaryName) {
		this.summaryName = summaryName;
	}

	public Integer getDataCount() {
		return dataCount;
	}

	public void setDataCount(Integer dataCount) {
		this.dataCount = dataCount;
	}

}
