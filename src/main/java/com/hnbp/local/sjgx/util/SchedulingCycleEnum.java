package com.hnbp.local.sjgx.util;

/**
 * Created by Administrator on 2019-08-05.
 *
 * <AUTHOR>
 * @version V1.0
 * Update Logs:
 * ****************************************************
 * Name:
 * Date:
 * Description:
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: ${todo}(用一句话描述该文件做什么)
 * @date ${date} ${time}
 */
public enum SchedulingCycleEnum {
    YEAR("年份"), HALFYEAR("半年"), QUARTER("季度"), MONTH("年月"), DAY("日");

    private String name;

    SchedulingCycleEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static SchedulingCycleEnum getSchedulingCycleEnum(int ordinal) {
        SchedulingCycleEnum[] schedulingCycleEnums = SchedulingCycleEnum.values();
        for (int i = 0; i < schedulingCycleEnums.length; i++) {
            if (schedulingCycleEnums[i].ordinal() == ordinal) {
                return schedulingCycleEnums[i];
            }
        }
        return null;
    }


}
