package com.hnbp.local.sjgx.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.StringUtils;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.common.core.utils.zhzs.util.ParameterUtil;
import com.hnbp.local.sjgx.mapper.DataSharingMapper;
import com.hnbp.local.sjgx.model.SharingTree;
import com.hnbp.local.sjgx.model.SubmissionInfo;
import com.hnbp.local.sjgx.model.SubmissionInfoxs;
import com.hnbp.local.sjgx.model.SubmissionTotal;
import com.hnbp.local.sjgx.service.DataSharingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: 数据共享Impl
 * @date 2024-10-22
 */
@Service
public class DataSharingServiceImpl implements DataSharingService {

    @Autowired
    private DataSharingMapper dataSharingMapper;

    @Override
    public List<SubmissionTotal> getsjbstj(SubmissionInfo map) {
        List<SubmissionInfo> getsjbstj = dataSharingMapper.getsjbstj(map);
        List<SubmissionTotal> list = new ArrayList<>();
        for (SubmissionInfo info : getsjbstj) {
            map.setFtablename(info.getFtablename());
            map.setFuserid(info.getFuserid());
            map.setFdispatchCycle(info.getFdispatchCycle());
            if (StrUtil.isNotBlank(map.getFyearend())) {
                info.setFyear(map.getFyearstart() + " ~ " + map.getFyearend());
            } else {
                info.setFyear(map.getFyear());
            }
            List<SubmissionInfoxs> getsjbstjxs = dataSharingMapper.getsjbstjxs(map);
            SubmissionTotal submissionTotal = new SubmissionTotal();
            submissionTotal.setFdwmc(info.getFusername());
            submissionTotal.setFsjx(info.getFname());
            submissionTotal.setFyear(info.getFyear());
            submissionTotal.setFddzq(info.getFdispatchCycle());
            if (!getsjbstjxs.isEmpty()) {
                SubmissionInfoxs submissionInfoxs = getsjbstjxs.get(0);
                int total = 0;
                switch (info.getFdispatchCycle()) {
                    case "0":
                        submissionTotal.setTotal(String.valueOf(submissionInfoxs.getFcount()));
                        break;
                    case "1":
                        SubmissionInfoxs submissionInfoxs2 = getsjbstjxs.get(1);
                        submissionTotal.setFcount6(String.valueOf(submissionInfoxs.getFcount()));
                        submissionTotal.setFcount12(String.valueOf(submissionInfoxs2.getFcount()));
                        submissionTotal.setTotal(String.valueOf(submissionInfoxs2.getFcount() + submissionInfoxs.getFcount()));

                        break;
                    case "2":
                        for (SubmissionInfoxs getsjbstjx : getsjbstjxs) {
                            total = total + getsjbstjx.getFcount();
                            if (StringUtils.isNotBlank(getsjbstjx.getF_sys_quarter())){
                                switch (getsjbstjx.getF_sys_quarter()) {
                                    case "1":
                                        submissionTotal.setFcount3(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "2":
                                        submissionTotal.setFcount6(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "3":
                                        submissionTotal.setFcount9(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "4":
                                        submissionTotal.setFcount12(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                }
                            }

                        }
                        submissionTotal.setTotal(String.valueOf(total));
                        break;
                    case "3":
                        for (SubmissionInfoxs getsjbstjx : getsjbstjxs) {
                            total = total + getsjbstjx.getFcount();
                            if (StringUtils.isNotBlank(getsjbstjx.getF_sys_month())){
                                switch (getsjbstjx.getF_sys_month()) {
                                    case "01":
                                        submissionTotal.setFcount1(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "02":
                                        submissionTotal.setFcount2(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "03":
                                        submissionTotal.setFcount3(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "04":
                                        submissionTotal.setFcount4(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "05":
                                        submissionTotal.setFcount5(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "06":
                                        submissionTotal.setFcount6(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "07":
                                        submissionTotal.setFcount7(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "08":
                                        submissionTotal.setFcount8(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "09":
                                        submissionTotal.setFcount9(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "10":
                                        submissionTotal.setFcount10(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "11":
                                        submissionTotal.setFcount11(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                    case "12":
                                        submissionTotal.setFcount12(String.valueOf(getsjbstjx.getFcount()));
                                        break;
                                }
                            }
                            submissionTotal.setTotal(String.valueOf(total));


                        }
                        break;
                }
            }
            list.add(submissionTotal);
        }
        return list;
    }


    @Override
    public List<SharingTree> queryUserDataSharingTtree(Map<String, Object> parameterMap) {
        //查询用户树权限
        List<HashMap> lookUserTreePermissions = dataSharingMapper.lookUserTreePermissions(parameterMap);
        List<SharingTree> returnList = new ArrayList<>();
        if (lookUserTreePermissions.size() > 0) {
            String useridStr = (String) lookUserTreePermissions.get(0).get("fnumber");
            if (!(useridStr == null) && useridStr.length() > 0) {
                useridStr = "'" + useridStr.replace(",", "','") + "'";
                parameterMap.put("useridStr", useridStr);
                List<SharingTree> flatList = dataSharingMapper.queryUserDataSharingTtree(parameterMap);
                // 构建树形结构
                returnList = buildTreeStructure(flatList);
            }
        }

        return returnList;
    }

    /**
     * 构建树形结构
     * 根据 id 和 parentId 构建父子关系，parentId 为 "000000" 的是根节点
     *
     * @param flatList 平铺的节点列表
     * @return 树形结构的根节点列表
     */
    private List<SharingTree> buildTreeStructure(List<SharingTree> flatList) {
        if (flatList == null || flatList.isEmpty()) {
            return new ArrayList<>();
        }

        // 创建节点映射，用于快速查找
        Map<String, SharingTree> nodeMap = new HashMap<>();
        List<SharingTree> rootNodes = new ArrayList<>();

        // 初始化所有节点的children列表，并建立id到节点的映射
        for (SharingTree node : flatList) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }

        // 构建父子关系
        for (SharingTree node : flatList) {
            String parentId = node.getParentId();

            // 如果是根节点（parentId为"000000"）
            if ("000000".equals(parentId)) {
                rootNodes.add(node);
            }
            // 如果有父节点且父节点存在于映射中
            else if (parentId != null && !parentId.isEmpty() && nodeMap.containsKey(parentId)) {
                SharingTree parentNode = nodeMap.get(parentId);
                parentNode.getChildren().add(node);
            }
        }

        return rootNodes;
    }
}
