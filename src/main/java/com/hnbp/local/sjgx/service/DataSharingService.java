package com.hnbp.local.sjgx.service;

import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.local.sjgx.model.SharingTree;
import com.hnbp.local.sjgx.model.SubmissionInfo;
import com.hnbp.local.sjgx.model.SubmissionTotal;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: Service
 * @date 2024-10-22
 */
public interface DataSharingService {
    List<SubmissionTotal> getsjbstj(SubmissionInfo map);

    List<SharingTree> queryUserDataSharingTtree(Map<String, Object> parameterMap);
}
