package com.hnbp.local.sjgx.mapper;

import com.hnbp.local.sjgx.model.SharingTree;
import com.hnbp.local.sjgx.model.SubmissionInfo;
import com.hnbp.local.sjgx.model.SubmissionInfoxs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 数据共享Mapper
 * @date 2024-10-22
 */
@Mapper
public interface DataSharingMapper {
    List<SubmissionInfo> getsjbstj(SubmissionInfo map);

    List<SubmissionInfoxs> getsjbstjxs(SubmissionInfo submissionInfo);

    List<HashMap> lookUserTreePermissions(Map<String, Object> parameterMap);

    List<SharingTree> queryUserDataSharingTtree(Map<String, Object> parameterMap);
}
