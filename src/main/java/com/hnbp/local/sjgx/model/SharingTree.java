package com.hnbp.local.sjgx.model;

import java.util.List;

/**
 * ━━━━━━神兽保佑━━━━━━
 * 　　　┏┓　　　┏┓
 * 　　┏┛┻━━━┛┻┓
 * 　　┃　　　　　　　┃
 * 　　┃　　　━　　　┃
 * 　　┃　┳┛　┗┳　┃
 * 　　┃　　　　　　　┃
 * 　　┃　　　┻　　　┃
 * 　　┃　　　　　　　┃
 * 　　┗━┓　　　┏━┛
 * 　　　　┃　　　┃
 * 　　　　┃　　　┃
 * 　　　　┃　　　┗━━━┓
 * 　　　　┃　　　　　　　┣┓
 * 　　　　┃　　　　　　　┏┛
 * 　　　　┗┓┓┏━┳┓┏┛
 * 　　　　　┃┫┫　┃┫┫
 * 　　　　　┗┻┛　┗┻┛
 * ━━━━━━永无BUG━━━━━━
 *
 * <AUTHOR>
 * @date 2019/8/13 18:20
 * @Title 数据共享树
 */
public class SharingTree {
    private String id;//
    private String title;//标题
    private String parentId;//父id
    private String fljid;//用户连接id
    private String fjhid;//集合id
    private String checkArr="0";//选择框

    private List<SharingTree> children;

    public String getFjhid() {
        return fjhid;
    }

    public void setFjhid(String fjhid) {
        this.fjhid = fjhid;
    }

    public String getCheckArr() {
        return checkArr;
    }

    public void setCheckArr(String checkArr) {
        this.checkArr = checkArr;
    }

    public String getFljid() {
        return fljid;
    }

    public void setFljid(String fljid) {
        this.fljid = fljid;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public List<SharingTree> getChildren() {
        return children;
    }

    public void setChildren(List<SharingTree> children) {
        this.children = children;
    }
}
