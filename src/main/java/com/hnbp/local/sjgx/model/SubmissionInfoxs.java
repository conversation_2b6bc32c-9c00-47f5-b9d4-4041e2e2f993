package com.hnbp.local.sjgx.model;

public class SubmissionInfoxs {

    private String ftablename;
    private String f_sys_year;
    private String f_sys_month;
    private String f_sys_halfyear;
    private String f_sys_quarter;
    private String f_sys_other;
    private String userid;
    private String fcreatetime;
    private String flastcreatetime;
    private Integer fcount;

    public String getFtablename() {
        return ftablename;
    }

    public void setFtablename(String ftablename) {
        this.ftablename = ftablename;
    }

    public String getF_sys_year() {
        return f_sys_year;
    }

    public void setF_sys_year(String f_sys_year) {
        this.f_sys_year = f_sys_year;
    }

    public String getF_sys_month() {
        return f_sys_month;
    }

    public void setF_sys_month(String f_sys_month) {
        this.f_sys_month = f_sys_month;
    }

    public String getF_sys_halfyear() {
        return f_sys_halfyear;
    }

    public void setF_sys_halfyear(String f_sys_halfyear) {
        this.f_sys_halfyear = f_sys_halfyear;
    }

    public String getF_sys_quarter() {
        return f_sys_quarter;
    }

    public void setF_sys_quarter(String f_sys_quarter) {
        this.f_sys_quarter = f_sys_quarter;
    }

    public String getF_sys_other() {
        return f_sys_other;
    }

    public void setF_sys_other(String f_sys_other) {
        this.f_sys_other = f_sys_other;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getFcreatetime() {
        return fcreatetime;
    }

    public void setFcreatetime(String fcreatetime) {
        this.fcreatetime = fcreatetime;
    }

    public String getFlastcreatetime() {
        return flastcreatetime;
    }

    public void setFlastcreatetime(String flastcreatetime) {
        this.flastcreatetime = flastcreatetime;
    }

    public Integer getFcount() {
        return fcount;
    }

    public void setFcount(Integer fcount) {
        this.fcount = fcount;
    }
}
