package com.hnbp.local.sjgx.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.common.core.utils.zhzs.paging.PageParameter;
import com.hnbp.common.core.utils.zhzs.util.ParameterUtil;
import com.hnbp.common.security.utils.SecurityUtils;
import com.hnbp.local.sjgx.model.SharingTree;
import com.hnbp.local.sjgx.model.SubmissionInfo;
import com.hnbp.local.sjgx.model.SubmissionTotal;
import com.hnbp.local.sjgx.service.DataSharingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 数据共享Controller
 * @date 2024-10-22
 */
@Controller
@RequestMapping("sjbs")
@ResponseBody
public class DataSharingController {

    @Autowired
    private DataSharingService dataSharingService;

    @RequestMapping("getsjbstj")
    @ResponseBody
    public ResultMsg getsjbstj(@RequestParam Map<String, Object> parameterMap) throws IOException, SQLException {

        String fstartYear = (String) parameterMap.get("start_year");
        String fendYear = (String) parameterMap.get("end_year");
        String fdwmc = (String) parameterMap.get("dwmc");
        String fuserid = (String) parameterMap.get("fuserid");
        SubmissionInfo map = new SubmissionInfo();
        map.setFusername(fdwmc);
        map.setFyear(fstartYear);
        map.setFyearstart(fstartYear);
        map.setFyearend(fendYear);
        map.setFuserid(fuserid);
        List<SubmissionTotal> getsjbstj = dataSharingService.getsjbstj(map);
        return ResultMsg.success(getsjbstj);
    }

    /**
     * 查询用户数据共享树
     *
     * @return void
     * @Title queryUserDataSharingTtree
     * @date 2019/8/14 11:18
     * <AUTHOR>
     */
    @RequestMapping("/queryUserDataSharingTtree2")
    @ResponseBody
    public ResultMsg queryUserDataSharingTtree2(@RequestParam Map<String, Object> parameterMap) throws Exception {

        if (!parameterMap.containsKey("userid")) {
            parameterMap.put("userid", SecurityUtils.getUserId());
        }
        List<SharingTree> returnVal = dataSharingService.queryUserDataSharingTtree(parameterMap);
        return ResultMsg.success(returnVal);
    }
}
