<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Tue Oct 22 17:04:39 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sjgx.mapper.DataSharingMapper">

    <select id="getsjbstj" parameterType="com.hnbp.local.sjgx.model.SubmissionInfo"
            resultType="com.hnbp.local.sjgx.model.SubmissionInfo">
        select fid,fusername,fname,ftablename,fdispatchCycle,fuserid from v_sjbs_info where fdispatchCycle in ('0','1','2','3','4')
        <if test="fusername != null and fusername != ''">and
            fusername like concat('%',#{fusername},'%')
        </if>
        <if test="fuserid != null and fuserid != '' and fuserid.split(',').length > 0">
            and FUSERID IN
            <foreach collection="fuserid.split(',')" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getsjbstjxs" parameterType="com.hnbp.local.sjgx.model.SubmissionInfo"
            resultType="com.hnbp.local.sjgx.model.SubmissionInfoxs">
        select * from (
            select ftablename,f_sys_year,f_sys_month,f_sys_halfyear,f_sys_quarter,f_sys_other,userid,fcreatetime,flastcreatetime,fcount,
                   ROW_NUMBER() OVER (PARTITION BY
                        <choose>
                            <when test='fdispatchCycle == "0"'>f_sys_year</when>
                            <when test='fdispatchCycle == "1"'>f_sys_halfyear</when>
                            <when test='fdispatchCycle == "2"'>f_sys_quarter</when>
                            <when test='fdispatchCycle == "3"'>f_sys_month</when>
                            <otherwise>f_sys_month</otherwise>
                        </choose>
                    ORDER BY fcreatetime DESC) AS row_num
            from zhzs_bill_sjgx where ftablename=#{ftablename}
            <if test="fyearend != null and fyearend != ''">
                and f_sys_year &lt;= #{fyearend} and f_sys_year >= #{fyearstart}
            </if>
            <if test="fyearend == null or fyearend == ''">
                and f_sys_year = #{fyear}
            </if>
            <if test="fuserid != null and fuserid != '' and fuserid.split(',').length > 0">
                and userid IN
                <foreach collection="fuserid.split(',')" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            order by f_sys_month,f_sys_halfyear,f_sys_quarter,f_sys_other
          )where row_num = 1
    </select>

    <!--查询用户树权限 lookUserTreePermissions-->
    <select id="lookUserTreePermissions" parameterType="java.util.HashMap" resultType="java.util.HashMap">
        select a.fuserid, a.fnumber from TB_DW_ZHZS_USERTREE_GEN  a where a.fuserid = #{userid}
    </select>
    <!-- queryUserDataSharingTtree  查询用户数据共享树-->
    <select id="queryUserDataSharingTtree" parameterType="java.util.HashMap" resultType="com.hnbp.local.sjgx.model.SharingTree">
        select b.fid id, b.fsjdmc title, b.fpx, b.fpid parentId, COALESCE(CASE TRIM(b.fljid) WHEN '' THEN NULL ELSE b.fljid END, b.fsjdmc) fljid, b.fcjdid,b.fjhid
        from TB_DW_ZHZS_SJHXX_GEN a
        inner join TB_DW_ZHZS_SJDXX_GEN b
        on a.fid = b.fjhid
        where a.fjhbh in ('SXJH-0005')
        <if test="userid != null and userid != ''">and
            b.fid in (select fnumber from TB_DW_ZHZS_USERTREE_GEN uu where uu.fuserid = #{userid})
        </if>
        order by fpx
    </select>

</mapper>
