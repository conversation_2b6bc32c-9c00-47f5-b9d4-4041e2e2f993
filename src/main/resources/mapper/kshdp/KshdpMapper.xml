<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.kshdp.mapper.KshdpMapper">

    <select id="nsgmfx" resultType="java.util.Map">
        WITH T_RAW_DATA AS (
            SELECT
                fnf,
                -- 按条件统计户数 (fhs)
                SUM(CASE WHEN fhj  &lt; 50 * 10000 THEN 1 ELSE 0 END)    AS fhs_lt50,
                SUM(CASE WHEN fhj >= 50 * 10000 THEN 1 ELSE 0 END)    AS fhs_gt50,
                SUM(CASE WHEN fhj >= 100 * 10000 THEN 1 ELSE 0 END)   AS fhs_gt100,
                SUM(CASE WHEN fhj >= 500 * 10000 THEN 1 ELSE 0 END)   AS fhs_gt500,
                SUM(CASE WHEN fhj >= 1000 * 10000 THEN 1 ELSE 0 END)  AS fhs_gt1000,
                SUM(CASE WHEN fhj >= 5000 * 10000 THEN 1 ELSE 0 END)  AS fhs_gt5000,
                SUM(CASE WHEN fhj >= 10000 * 10000 THEN 1 ELSE 0 END) AS fhs_gt10000,
                -- 按条件统计合计金额 (fhj)
                SUM(CASE WHEN fhj  &lt; 50 * 10000 THEN fhj ELSE 0 END)    AS fhj_lt50,
                SUM(CASE WHEN fhj >= 50 * 10000 THEN fhj ELSE 0 END)    AS fhj_gt50,
                SUM(CASE WHEN fhj >= 100 * 10000 THEN fhj ELSE 0 END)   AS fhj_gt100,
                SUM(CASE WHEN fhj >= 500 * 10000 THEN fhj ELSE 0 END)   AS fhj_gt500,
                SUM(CASE WHEN fhj >= 1000 * 10000 THEN fhj ELSE 0 END)  AS fhj_gt1000,
                SUM(CASE WHEN fhj >= 5000 * 10000 THEN fhj ELSE 0 END)  AS fhj_gt5000,
                SUM(CASE WHEN fhj >= 10000 * 10000 THEN fhj ELSE 0 END) AS fhj_gt10000
            FROM (
                SELECT
                    FNSRMC,
                    YEAR(FRKRQ) AS fnf,
                    SUM(FHJ) AS fhj
                FROM TB_DW_SRFX_SRFX_MAIN
                WHERE 1=1
                AND (
                    FRKRQ BETWEEN to_date(concat(#{fyear}, '-01'), 'YYYY-MM') AND last_day(to_date(concat(#{fyear}, '-', #{fmonth}), 'YYYY-MM'))
                    OR FRKRQ BETWEEN dateadd(YEAR, -1, to_date(concat(#{fyear}, '-01'), 'YYYY-MM')) AND dateadd(YEAR, -1, last_day(to_date(concat(#{fyear}, '-', #{fmonth}), 'YYYY-MM')))
                )
                AND FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
                GROUP BY FNSRMC, YEAR(FRKRQ)
            ) t1
            GROUP BY fnf
        ),
        T_FINAL_WIDE_DATA AS (
            SELECT * FROM (
            SELECT
                t.*,
                -- 获取同期的各项指标
                LEAD(fhs_lt50, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_lt50_qn,
                LEAD(fhs_gt50, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt50_qn,
                LEAD(fhs_gt100, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt100_qn,
                LEAD(fhs_gt500, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt500_qn,
                LEAD(fhs_gt1000, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt1000_qn,
                LEAD(fhs_gt5000, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt5000_qn,
                LEAD(fhs_gt10000, 1, 0) OVER(ORDER BY fnf DESC) AS fhs_gt10000_qn,
                LEAD(fhj_lt50, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_lt50_qn,
                LEAD(fhj_gt50, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt50_qn,
                LEAD(fhj_gt100, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt100_qn,
                LEAD(fhj_gt500, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt500_qn,
                LEAD(fhj_gt1000, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt1000_qn,
                LEAD(fhj_gt5000, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt5000_qn,
                LEAD(fhj_gt10000, 1, 0) OVER(ORDER BY fnf DESC) AS fhj_gt10000_qn,
                -- 计算总户数和总金额，用于计算占比
                (fhs_lt50 + fhs_gt50) AS fhs_total_jn,
                (fhj_lt50 + fhj_gt50) AS fhj_total_jn
            FROM T_RAW_DATA t
            ) WHERE fnf = #{fyear}
        )
        SELECT
            '大于一亿' AS fgm,
            fnf,
            fhs_gt10000 AS fhsjn,
            fhs_gt10000_qn AS fhsqn,
            fhj_gt10000 AS fhjjn,
            fhj_gt10000_qn AS fhjqn,
            case when fhs_gt10000_qn = 0 then null else ROUND((fhs_gt10000 - fhs_gt10000_qn)/fhs_gt10000_qn * 100, 2) end AS fhszjf,
            case when fhj_gt10000_qn = 0 then null else ROUND((fhj_gt10000 - fhj_gt10000_qn)/fhj_gt10000_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt10000 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt10000 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '大于五千万' AS fgm,
            fnf,
            fhs_gt5000 AS fhsjn,
            fhs_gt5000_qn AS fhsqn,
            fhj_gt5000 AS fhjjn,
            fhj_gt5000_qn AS fhjqn,
            case when fhs_gt5000_qn = 0 then null else ROUND((fhs_gt5000 - fhs_gt5000_qn)/fhs_gt5000_qn * 100, 2) end AS fhszjf,
            case when fhj_gt5000_qn = 0 then null else ROUND((fhj_gt5000 - fhj_gt5000_qn)/fhj_gt5000_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt5000 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt5000 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '大于一千万' AS fgm,
            fnf,
            fhs_gt1000 AS fhsjn,
            fhs_gt1000_qn AS fhsqn,
            fhj_gt1000 AS fhjjn,
            fhj_gt1000_qn AS fhjqn,
            case when fhs_gt1000_qn = 0 then null else ROUND((fhs_gt1000 - fhs_gt1000_qn)/fhs_gt1000_qn * 100, 2) end AS fhszjf,
            case when fhj_gt1000_qn = 0 then null else ROUND((fhj_gt1000 - fhj_gt1000_qn)/fhj_gt1000_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt1000 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt1000 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '大于五百万' AS fgm,
            fnf,
            fhs_gt500 AS fhsjn,
            fhs_gt500_qn AS fhsqn,
            fhj_gt500 AS fhjjn,
            fhj_gt500_qn AS fhjqn,
            case when fhs_gt500_qn = 0 then null else ROUND((fhs_gt500 - fhs_gt500_qn)/fhs_gt500_qn * 100, 2) end AS fhszjf,
            case when fhj_gt500_qn = 0 then null else ROUND((fhj_gt500 - fhj_gt500_qn)/fhj_gt500_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt500 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt500 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '大于一百万' AS fgm,
            fnf,
            fhs_gt100 AS fhsjn,
            fhs_gt100_qn AS fhsqn,
            fhj_gt100 AS fhjjn,
            fhj_gt100_qn AS fhjqn,
            case when fhs_gt100_qn = 0 then null else ROUND((fhs_gt100 - fhs_gt100_qn)/fhs_gt100_qn * 100, 2) end AS fhszjf,
            case when fhj_gt100_qn = 0 then null else ROUND((fhj_gt100 - fhj_gt100_qn)/fhj_gt100_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt100 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt100 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '大于五十万' AS fgm,
            fnf,
            fhs_gt50 AS fhsjn,
            fhs_gt50_qn AS fhsqn,
            fhj_gt50 AS fhjjn,
            fhj_gt50_qn AS fhjqn,
            case when fhs_gt50_qn = 0 then null else ROUND((fhs_gt50 - fhs_gt50_qn)/fhs_gt50_qn * 100, 2) end AS fhszjf,
            case when fhj_gt50_qn = 0 then null else ROUND((fhj_gt50 - fhj_gt50_qn)/fhj_gt50_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_gt50 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_gt50 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
        UNION ALL
        SELECT
            '小于五十万' AS fgm,
            fnf,
            fhs_lt50 AS fhsjn,
            fhs_lt50_qn AS fhsqn,
            fhj_lt50 AS fhjjn,
            fhj_lt50_qn AS fhjqn,
            case when fhs_lt50_qn = 0 then null else ROUND((fhs_lt50 - fhs_lt50_qn)/fhs_lt50_qn * 100, 2) end AS fhszjf,
            case when fhj_lt50_qn = 0 then null else ROUND((fhj_lt50 - fhj_lt50_qn)/fhj_lt50_qn * 100, 2) end AS fhjzf,
            ROUND(fhs_lt50 * 100.0 / NULLIF(fhs_total_jn, 0), 2) AS fhszb,
            ROUND(fhj_lt50 * 100.0 / NULLIF(fhj_total_jn, 0), 2) AS fhjzb
        FROM T_FINAL_WIDE_DATA
    </select>

    <select id="nsgmfxTrend" resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        WITH
        base AS (
            SELECT
                fnf,
                -- 按条件统计户数 (fhs)
                SUM(CASE WHEN fhj  &lt; 50 * 10000 THEN 1 ELSE 0 END)    AS fhs_lt50,
                SUM(CASE WHEN fhj >= 50 * 10000 THEN 1 ELSE 0 END)    AS fhs_gt50,
                SUM(CASE WHEN fhj >= 100 * 10000 THEN 1 ELSE 0 END)   AS fhs_gt100,
                SUM(CASE WHEN fhj >= 500 * 10000 THEN 1 ELSE 0 END)   AS fhs_gt500,
                SUM(CASE WHEN fhj >= 1000 * 10000 THEN 1 ELSE 0 END)  AS fhs_gt1000,
                SUM(CASE WHEN fhj >= 5000 * 10000 THEN 1 ELSE 0 END)  AS fhs_gt5000,
                SUM(CASE WHEN fhj >= 10000 * 10000 THEN 1 ELSE 0 END) AS fhs_gt10000,
                -- 按条件统计合计金额 (fhj)
                SUM(CASE WHEN fhj  &lt; 50 * 10000 THEN fhj ELSE 0 END)    AS fhj_lt50,
                SUM(CASE WHEN fhj >= 50 * 10000 THEN fhj ELSE 0 END)    AS fhj_gt50,
                SUM(CASE WHEN fhj >= 100 * 10000 THEN fhj ELSE 0 END)   AS fhj_gt100,
                SUM(CASE WHEN fhj >= 500 * 10000 THEN fhj ELSE 0 END)   AS fhj_gt500,
                SUM(CASE WHEN fhj >= 1000 * 10000 THEN fhj ELSE 0 END)  AS fhj_gt1000,
                SUM(CASE WHEN fhj >= 5000 * 10000 THEN fhj ELSE 0 END)  AS fhj_gt5000,
                SUM(CASE WHEN fhj >= 10000 * 10000 THEN fhj ELSE 0 END) AS fhj_gt10000
            FROM (
                SELECT FNSRMC, FNF, SUM(FHJ) fhj
                FROM TB_DW_SRFX_SRFX_MAIN
                WHERE 1=1
                AND FNF BETWEEN #{fyear}-5 AND #{fyear}
                AND FYF &lt;= #{fmonth}
                AND FZSXM IN ('教育费附加','地方教育附加','增值税','企业所得税','契税','土地增值税','耕地占用税','资源税','房产税','城市维护建设税','车辆购置税','城镇土地使用税','车船税','个人所得税','烟叶税','印花税','船舶吨税','消费税','环境保护税','营业税')
                GROUP BY FNSRMC, FNF
            ) t1 GROUP BY fnf
        )
        SELECT fnf xAxis
            , <choose>
                    <when test="fxm=='小于五十万'">fhs_lt50</when>
                    <when test="fxm=='大于五十万'">fhs_gt50</when>
                    <when test="fxm=='大于一百万'">fhs_gt100</when>
                    <when test="fxm=='大于五百万'">fhs_gt500</when>
                    <when test="fxm=='大于一千万'">fhs_gt1000</when>
                    <when test="fxm=='大于五千万'">fhs_gt5000</when>
                    <when test="fxm=='大于一亿'">fhs_gt10000</when>
                </choose> yAxis
            , '户数' legend, 'bar' type FROM base
        UNION ALL
        SELECT fnf xAxis
            , <choose>
                    <when test="fxm=='小于五十万'">fhj_lt50</when>
                    <when test="fxm=='大于五十万'">fhj_gt50</when>
                    <when test="fxm=='大于一百万'">fhj_gt100</when>
                    <when test="fxm=='大于五百万'">fhj_gt500</when>
                    <when test="fxm=='大于一千万'">fhj_gt1000</when>
                    <when test="fxm=='大于五千万'">fhj_gt5000</when>
                    <when test="fxm=='大于一亿'">fhj_gt10000</when>
                </choose> yAxis
            , '税收' legend, 'bar' type FROM base
    </select>

    <select id="getTaxDataByDepartment" resultType="java.util.Map">
        WITH
        ss AS (
            SELECT t1.FBMMC, SUM(t2.fsssrhj-t2.fhgdz) AS fsssrhj
            FROM TB_DW_ZHZS_SSDP_HYBMFL_GEN t1
            LEFT JOIN tb_dm_zhzs_ssdp_gen t2 ON t1.FHY=t2.FJHXMMC AND t2.fsjly = '税务汇总分行业分税种收入表'
            WHERE t2.FNF=#{fyear} AND t2.FYF=#{fmonth}
            GROUP BY t1.FBMMC
        )
        , czxx AS (
            SELECT t1.FBMMC, SUM(t2.FCZ) AS fcz
            FROM TB_DW_ZHZS_SSDP_HYBMFL_GEN t1
            LEFT JOIN TB_DW_ZHZS_HYCZXX_GEN t2 ON t1.FZBMC=t2.FZBMC
            WHERE t2.FNF=#{fyear} AND t2.FJD=CEIL(#{fmonth} / 3)
            GROUP BY t1.FBMMC
        )
        , dkxx AS (
            SELECT t1.FBMMC, SUM(t2.FDKYE) AS fdkye
            FROM TB_DW_ZHZS_SSDP_HYBMFL_GEN t1
            LEFT JOIN TB_DW_ZHZS_HYDKXX_GEN t2 ON t1.FHY=t2.FHY
            WHERE t2.FNF=#{fyear} AND t2.FYF=#{fmonth}
            GROUP BY t1.FBMMC
        )
        -- 使用 UNION ALL + GROUP BY 的方式进行合并
        SELECT
            FBMMC fbmmc,
            SUM(fsssrhj) AS fsssrhj,
            SUM(fcz) AS fcz,
            SUM(fdkye) AS fdkye
        FROM (
            SELECT FBMMC, fsssrhj, NULL AS fcz, NULL AS fdkye FROM ss
            UNION ALL
            SELECT FBMMC, NULL AS fsssrhj, fcz, NULL AS fdkye FROM czxx
            UNION ALL
            SELECT FBMMC, NULL AS fsssrhj, NULL AS fcz, fdkye FROM dkxx
        ) AS all_data
        WHERE FBMMC IS NOT NULL
        GROUP BY FBMMC
    </select>

    <select id="getTaxDataByDepartmentTrend" resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        select fnf xAxis,fsssrhj yAxis, '税收' legend, 'bar' type
        from(
        select
        max(tsh.fbmmc) fbmmc,tdzsg.fnf,
        sum(tdzsg.fsssrhj-tdzsg.fhgdz) fsssrhj
        from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
        left join tb_dm_zhzs_ssdp_gen tdzsg on tdzsg.fjhxmmc = tsh.fhy
        where 1=1
        and tdzsg.fsjly = '税务汇总分行业分税种收入表'
        AND tsh.fbmmc = #{fxm}
        AND tdzsg.fnf &lt;= #{fyear}
        AND tdzsg.fyf = #{fmonth}
        group by tdzsg.fnf
        order by tdzsg.fnf desc
        limit 5
        ) t1
        UNION ALL
        select fnf xAxis,fsssrhj yAxis,'产值' legend,'bar' type
        from(
        select max(tsh.fbmmc) fbmmc,zbb.fnf fnf,sum(zbb.fcz) fsssrhj
        from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
        left join TB_DW_ZHZS_HYCZXX_GEN zbb on zbb.fzbmc like tsh.fzbmc
        where 1=1
        AND tsh.fbmmc = #{fxm}
        AND zbb.fnf &lt;= #{fyear}
        AND zbb.fjd = CEIL(#{fmonth} / 3)
        group by zbb.fnf
        order by zbb.fnf desc
        limit 5
        ) t1
        UNION ALL
        select fnf xAxis,fsssrhj yAxis,'贷款余额' legend,'bar' type
        from(
        select max(tsh.fbmmc) fbmmc,zbb.fnf fnf,sum(zbb.fdkye) fsssrhj
        from TB_DW_ZHZS_SSDP_HYBMFL_GEN tsh
        left join TB_DW_ZHZS_HYDKXX_GEN zbb on zbb.fhy like tsh.FHY
        where 1=1
        AND tsh.fbmmc = #{fxm}
        AND zbb.fnf &lt;= #{fyear}
        AND zbb.fyf = #{fmonth}
        group by zbb.fnf
        order by zbb.fnf desc
        limit 5
        ) t1
        order by legend ,xAxis asc
    </select>

    <select id="getQssjfhytj" resultType="java.util.Map">
        select *,
               fqse - fsqqse fzje,
               round(case when fsqqse = 0 or fsqqse is null then 100 else (fqse - fsqqse)/fsqqse * 100 end,2) fzjb
        from (
              select '合计' fzsxm,
                     round(sum(case when substr(ftjsj,1,7) = #{fssny} then fybtse else 0 end)/10000,2) fqse,
                     round(sum(case when substr(ftjsj,1,7) = #{fdbny} then fybtse else 0 end)/10000,2) fsqqse
              from tb_dw_cyjs_qssjxx_gen
              where
                  (ftjsj between to_date(#{fssny},'YYYY-MM-DD') and last_day(to_date(#{fssny},'YYYY-MM'))
                      or
                   ftjsj between to_date(#{fdbny},'YYYY-MM-DD') and last_day(to_date(#{fdbny},'YYYY-MM')))
                <if test="fqy != null and fqy != ''">
                  and fqy in (${fqyStr  })
                </if>
              union all
              select '欠税小计' fzsxm,
                     round(sum(case when substr(ftjsj,1,7) = #{fssny} then fybtse else 0 end)/10000,2) fqse,
                     round(sum(case when substr(ftjsj,1,7) = #{fdbny} then fybtse else 0 end)/10000,2) fsqqse
              from tb_dw_cyjs_qssjxx_gen
              where
                  (ftjsj between to_date(#{fssny},'YYYY-MM-DD') and last_day(to_date(#{fssny},'YYYY-MM'))
                      or
                   ftjsj between to_date(#{fdbny},'YYYY-MM-DD') and last_day(to_date(#{fdbny},'YYYY-MM')))
                and fzsxm like '%税'
                <if test="fqy != null and fqy != ''">
                    and fqy in (${fqyStr})
                </if>
              union all
              select '欠费小计' fzsxm,
                     round(sum(case when substr(ftjsj,1,7) = #{fssny} then fybtse else 0 end)/10000,2) fqse,
                     round(sum(case when substr(ftjsj,1,7) = #{fdbny} then fybtse else 0 end)/10000,2) fsqqse
              from tb_dw_cyjs_qssjxx_gen
              where (ftjsj between to_date(#{fssny},'YYYY-MM-DD') and last_day(to_date(#{fssny},'YYYY-MM'))
                  or
                     ftjsj between to_date(#{fdbny},'YYYY-MM-DD') and last_day(to_date(#{fdbny},'YYYY-MM')))
                and fzsxm not like '%税'
                <if test="fqy != null and fqy != ''">
                    and fqy in (${fqyStr})
                </if>
          )
    </select>

    <select id="getTaxDataByPSTIndustry" resultType="java.util.Map">
        SELECT
            FCY, FNF
            , ROUND(FSSSR, 2) FSSSR, ROUND(FSCZZ, 2) FSCZZ, ROUND(FYYSR, 2) FYYSR, ROUND(FLRSR, 2) FLRSR, ROUND(FDKJE, 2) FDKJE
            , CASE WHEN FSSSR_QN=0 THEN NULL ELSE ROUND((FSSSR-FSSSR_QN)/FSSSR_QN*100, 2) END fsssrZfb
            , CASE WHEN FSSSR_ALL=0 THEN NULL ELSE ROUND(FSSSR/FSSSR_ALL*100, 2) END fsssrZb
            , CASE WHEN FSCZZ_QN=0 THEN NULL ELSE ROUND((FSCZZ-FSCZZ_QN)/FSCZZ_QN*100, 2) END fsczzZfb
            , CASE WHEN FSCZZ_ALL=0 THEN NULL ELSE ROUND(FSCZZ/FSCZZ_ALL*100, 2) END fsczzZb
            , CASE WHEN FYYSR_QN=0 THEN NULL ELSE ROUND((FYYSR-FYYSR_QN)/FYYSR_QN*100, 2) END fyysrZfb
            , CASE WHEN FYYSR_ALL=0 THEN NULL ELSE ROUND(FYYSR/FYYSR_ALL*100, 2) END fyysrZb
            , CASE WHEN FLRSR_QN=0 THEN NULL ELSE ROUND((FLRSR-FLRSR_QN)/FLRSR_QN*100, 2) END flrsrZfb
            , CASE WHEN FLRSR_ALL=0 THEN NULL ELSE ROUND(FLRSR/FLRSR_ALL*100, 2) END flrsrZb	
            , CASE WHEN FDKJE_QN=0 THEN NULL ELSE ROUND((FDKJE-FDKJE_QN)/FDKJE_QN*100, 2) END fdkjeZfb
            , CASE WHEN FDKJE_ALL=0 THEN NULL ELSE ROUND(FDKJE/FDKJE_ALL*100, 2) END fdkyeZb	
        FROM (
            SELECT FCY, FNF
                , FSSSR, FSCZZ, FYYSR, FLRSR, FDKJE
                , LAG(FSSSR, 1, NULL) OVER(PARTITION BY FCY ORDER BY FNF) FSSSR_QN
                , SUM(FSSSR) OVER(PARTITION BY FNF) FSSSR_ALL
                , LAG(FSCZZ, 1, NULL) OVER(PARTITION BY FCY ORDER BY FNF) FSCZZ_QN
                , SUM(FSCZZ) OVER(PARTITION BY FNF) FSCZZ_ALL
                , LAG(FYYSR, 1, NULL) OVER(PARTITION BY FCY ORDER BY FNF) FYYSR_QN
                , SUM(FYYSR) OVER(PARTITION BY FNF) FYYSR_ALL
                , LAG(FLRSR, 1, NULL) OVER(PARTITION BY FCY ORDER BY FNF) FLRSR_QN
                , SUM(FLRSR) OVER(PARTITION BY FNF) FLRSR_ALL
                , LAG(FDKJE, 1, NULL) OVER(PARTITION BY FCY ORDER BY FNF) FDKJE_QN
                , SUM(FDKJE) OVER(PARTITION BY FNF) FDKJE_ALL
            FROM (
                SELECT REPLACE(FCY, '合计', '') FCY, FNF
                 , SUM(FSSSR) FSSSR, SUM(FSCZZ) FSCZZ, SUM(FYYSR) FYYSR, SUM(FLRSR) FLRSR, SUM(FDKJE) FDKJE
                FROM TB_DM_ZHZS_SSDP_CYSJ_GEN
                WHERE FSJLY='产业数据' AND FHYML IS NULL
                AND FNF IN (#{fyear}, #{fyear}-1) AND FYF=#{fmonth}
                GROUP BY FCY, FNF
            )
        )
        WHERE FNF=#{fyear}
        ORDER BY CASE
            WHEN FCY='第一产业' THEN 1
            WHEN FCY='第二产业' THEN 2
            WHEN FCY='第三产业' THEN 3
        END
    </select>

    <select id="getYearDataByPSTIndustry" resultType="com.hnbp.common.core.utils.zhzs.echarts.Bar.BarSeriesInitData">
        WITH 
        base AS (
        SELECT FCY, FNF
         , SUM(FSSSR) FSSSR, SUM(FSCZZ) FSCZZ, SUM(FYYSR) FYYSR, SUM(FLRSR) FLRSR, SUM(FDKJE) FDKJE
        FROM TB_DM_ZHZS_SSDP_CYSJ_GEN
        WHERE FSJLY='产业数据' AND FHYML IS NULL
        AND FCY=#{fxm}
        AND FNF BETWEEN #{fyear}-5 AND #{fyear} 
        AND FYF=#{fmonth}
        GROUP BY FCY, FNF
        )
        SELECT FNF xAxis, ROUND(FSSSR, 2) yAxis, '税收收入' legend, 'bar' type FROM base
        UNION ALL
        SELECT FNF xAxis, ROUND(FSCZZ, 2) yAxis, '生产总值' legend, 'bar' type FROM base
        UNION ALL
        SELECT FNF xAxis, ROUND(FYYSR, 2) yAxis, '营业收入' legend, 'bar' type FROM base
        UNION ALL
        SELECT FNF xAxis, ROUND(FLRSR, 2) yAxis, '利润收入' legend, 'bar' type FROM base
        UNION ALL
        SELECT FNF xAxis, ROUND(FDKJE, 2) yAxis, '贷款金额' legend, 'bar' type FROM base
        ORDER BY legend, xAxis ASC
    </select>

    <select id="getTaxDataByPSTIndustryFxm" resultType="java.util.Map">
        SELECT FHYML, FNF
            , ROUND(FSSSR, 2) FSSSR, ROUND(FSCZZ, 2) FSCZZ, ROUND(FYYSR, 2) FYYSR, ROUND(FLRSR, 2) FLRSR, ROUND(FDKJE, 2) FDKJE
            , CASE WHEN FSSSR_QN=0 THEN NULL ELSE ROUND((FSSSR-FSSSR_QN)/FSSSR_QN*100, 2) END fsssrZfb
            , CASE WHEN FSSSR_ALL=0 THEN NULL ELSE ROUND(FSSSR/FSSSR_ALL*100, 2) END fsssrZb
            , CASE WHEN FSCZZ_QN=0 THEN NULL ELSE ROUND((FSCZZ-FSCZZ_QN)/FSCZZ_QN*100, 2) END fsczzZfb
            , CASE WHEN FSCZZ_ALL=0 THEN NULL ELSE ROUND(FSCZZ/FSCZZ_ALL*100, 2) END fsczzZb
            , CASE WHEN FYYSR_QN=0 THEN NULL ELSE ROUND((FYYSR-FYYSR_QN)/FYYSR_QN*100, 2) END fyysrZfb
            , CASE WHEN FYYSR_ALL=0 THEN NULL ELSE ROUND(FYYSR/FYYSR_ALL*100, 2) END fyysrZb
            , CASE WHEN FLRSR_QN=0 THEN NULL ELSE ROUND((FLRSR-FLRSR_QN)/FLRSR_QN*100, 2) END flrsrZfb
            , CASE WHEN FLRSR_ALL=0 THEN NULL ELSE ROUND(FLRSR/FLRSR_ALL*100, 2) END flrsrZb	
            , CASE WHEN FDKJE_QN=0 THEN NULL ELSE ROUND((FDKJE-FDKJE_QN)/FDKJE_QN*100, 2) END fdkjeZfb
            , CASE WHEN FDKJE_ALL=0 THEN NULL ELSE ROUND(FDKJE/FDKJE_ALL*100, 2) END fdkyeZb	
        FROM (
            SELECT FHYML, FNF
                , FSSSR, FSCZZ, FYYSR, FLRSR, FDKJE
                , LAG(FSSSR, 1, NULL) OVER(PARTITION BY FHYML ORDER BY FNF) FSSSR_QN
                , SUM(FSSSR) OVER(PARTITION BY FNF) FSSSR_ALL
                , LAG(FSCZZ, 1, NULL) OVER(PARTITION BY FHYML ORDER BY FNF) FSCZZ_QN
                , SUM(FSCZZ) OVER(PARTITION BY FNF) FSCZZ_ALL
                , LAG(FYYSR, 1, NULL) OVER(PARTITION BY FHYML ORDER BY FNF) FYYSR_QN
                , SUM(FYYSR) OVER(PARTITION BY FNF) FYYSR_ALL
                , LAG(FLRSR, 1, NULL) OVER(PARTITION BY FHYML ORDER BY FNF) FLRSR_QN
                , SUM(FLRSR) OVER(PARTITION BY FNF) FLRSR_ALL
                , LAG(FDKJE, 1, NULL) OVER(PARTITION BY FHYML ORDER BY FNF) FDKJE_QN
                , SUM(FDKJE) OVER(PARTITION BY FNF) FDKJE_ALL
            FROM (
                SELECT FHYML, FNF
                 , SUM(FSSSR) FSSSR, SUM(FSCZZ) FSCZZ, SUM(FYYSR) FYYSR, SUM(FLRSR) FLRSR, SUM(FDKJE) FDKJE
                FROM TB_DM_ZHZS_SSDP_CYSJ_GEN
                WHERE FSJLY='产业数据'
                AND FCY=#{fxm} AND FHYML IS NOT NULL
                AND FNF IN (#{fyear}, #{fyear}-1) AND FYF=#{fmonth}
                GROUP BY FHYML, FNF
            )
        )
        WHERE FNF=#{fyear}        
    </select>
</mapper>
