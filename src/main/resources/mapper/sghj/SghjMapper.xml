<?xml version="1.0" encoding="UTF-8"?><!--

    Copyright 2004-2024 the original author or authors.

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

       https://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

--><!--Converted at: Tue Oct 22 17:04:39 CST 2024-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sghj.mapper.SghjMapper">
    <!-- 施工项目预警 -->
    <select id="sgxmyj" resultType="java.util.Map">
        WITH
        prediction AS (
            SELECT *
                , fcsZzs*0.05 fcsCswhjss, fcsZzs*0.03 fcsJyffj, fcsZzs*0.02 fcsDfjyfj
            FROM (
                SELECT *
                    , CASE WHEN FSGDWLX='本地' THEN FGCZJWY*#{zslZzsBd}   WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslZzsWd} END fcsZzs
                    , CASE WHEN FSGDWLX='本地' THEN NULL WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslQysdsWd} END fcsQysds
                    , CASE WHEN FSGDWLX='本地' THEN FGCZJWY*#{zslYhsBd} WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslYhsWd} END fcsYhs
                FROM (
                    SELECT FSGDW, MAX(FSHXYDM) FSHXYDM, MAX(TRIM(FSDLXSNHSW)) FSDLX
                        , CASE MAX(TRIM(FSDLXSNHSW)) WHEN '市内' THEN '本地' WHEN '市外' THEN '外地' END FSGDWLX
                        , SUM(REGEXP_SUBSTR(FGCZJWY, '\-?(\d+(\.\d*)?|\.\d+)')) FGCZJWY
                    FROM V_YJ_SGXMYJ <!-- 从原始表处理的视图 -->
                    <where>
                        <if test="fsgdwmc!=null and fsgdwmc!=''">
                            AND FSGDW LIKE CONCAT('%', #{fsgdwmc}, '%')
                        </if>
                        <if test="fshxydm!=null and fshxydm!=''">
                            AND FSHXYDM LIKE CONCAT('%', #{fshxydm}, '%')
                        </if>
                        <if test="fywtjrqS!=null and fywtjrqS!=''">
                            AND TO_DATE(FFZRQ, 'YYYY-MM-DD') >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                        </if>
                        <if test="fywtjrqE!=null and fywtjrqS!=''">
                            AND TO_DATE(FFZRQ, 'YYYY-MM-DD') &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                        </if>
                        <if test="fssqy	!=null and fssqy!=''">
                            AND FSSDQ IN
                            <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="fsgdwlxStr!=null and fsgdwlxStr!=''">
                            AND CASE TRIM(FSDLXSNHSW) WHEN '市内' THEN '1' WHEN '市外' THEN '2' END IN
                            <foreach collection="fsgdwlxStr.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </where>
                    GROUP BY FSGDW
                )
            )
        )
        , ss AS (
            SELECT p.FSGDW, ROUND(SUM(fzzs)/10000, 2) fhjZzs, ROUND(SUM(fqysds)/10000, 2) fhjQysds, ROUND(SUM(fyhs)/10000, 2) fhjYhs
                , ROUND(SUM(fcswhjss)/10000, 2) fhjCswhjss, ROUND(SUM(fjyffj)/10000, 2) fhjJyffj, ROUND(SUM(fdfjyfj)/10000, 2) fhjDfjyfj
                , MAX(FNSRSBH) FNSRSBH
            FROM prediction p
            INNER JOIN tb_dw_srfx_srfx_main s ON p.FSGDW=s.FNSRMC AND s.fzsxm IN ('增值税', '企业所得税', '印花税', '城市维护建设税','教育费附加','地方教育附加')
            <where>
                <if test="fywtjrqS!=null and fywtjrqS!=''">
                    AND s.FRKRQ >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                </if>
                <if test="fywtjrqE!=null and fywtjrqS!=''">
                    AND s.FRKRQ &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                </if>
            </where>
            GROUP BY p.FSGDW
        )
        SELECT
            GROUPING(p.FSGDW) fgrouping,
            CASE WHEN GROUPING(p.FSGDW) THEN '合计' ELSE p.FSGDW END FSGDW,
            CASE WHEN GROUPING(p.FSGDW) THEN '/' ELSE MAX(COALESCE(FSHXYDM, FNSRSBH)) END FSHXYDM,

            ROUND(SUM(p.FGCZJWY), 2) AS FGCZJWY,

            ROUND(SUM(p.fcsZzs), 2) AS fcsZzs,
            ROUND(SUM(s.fhjZzs), 2) AS fhjZzs,
            ROUND(COALESCE(SUM(p.fcsZzs), 0) - COALESCE(SUM(s.fhjZzs), 0), 2) AS fceZzs,

            ROUND(SUM(p.fcsQysds), 2) AS fcsQysds,
            ROUND(SUM(s.fhjQysds), 2) AS fhjQysds,
            ROUND(COALESCE(SUM(p.fcsQysds), 0) - COALESCE(SUM(s.fhjQysds), 0), 2) AS fceQysds,

            ROUND(SUM(p.fcsYhs), 2) AS fcsYhs,
            ROUND(SUM(s.fhjYhs), 2) AS fhjYhs,
            ROUND(COALESCE(SUM(p.fcsYhs), 0) - COALESCE(SUM(s.fhjYhs), 0), 2) AS fceYhs,

            ROUND(SUM(p.fcsCswhjss), 2) AS fcsCswhjss,
            ROUND(SUM(s.fhjCswhjss), 2) AS fhjCswhjss,
            ROUND(COALESCE(SUM(p.fcsCswhjss), 0) - COALESCE(SUM(s.fhjCswhjss), 0), 2) AS fceCswhjss,

            ROUND(SUM(p.fcsJyffj), 2) AS fcsJyffj,
            ROUND(SUM(s.fhjJyffj), 2) AS fhjJyffj,
            ROUND(COALESCE(SUM(p.fcsJyffj), 0) - COALESCE(SUM(s.fhjJyffj), 0), 2) AS fceJyffj,

            ROUND(SUM(p.fcsDfjyfj), 2) AS fcsDfjyfj,
            ROUND(SUM(s.fhjDfjyfj), 2) AS fhjDfjyfj,
            ROUND(COALESCE(SUM(p.fcsDfjyfj), 0) - COALESCE(SUM(s.fhjDfjyfj), 0), 2) AS fceDfjyfj
        FROM prediction p
        LEFT JOIN ss s ON p.FSGDW = s.FSGDW
        GROUP BY ROLLUP(p.FSGDW)
        ORDER BY fgrouping desc
    </select>

    <select id="sgxmyjDetail" resultType="java.util.Map">
        SELECT
            GROUPING(FXMMC) fgrouping,
            CASE WHEN GROUPING(FXMMC)=1 THEN '/' ELSE FSGDW END FSGDW,
            CASE WHEN GROUPING(FXMMC)=1 THEN '合计' ELSE FXMMC END FXMMC,
            CASE WHEN GROUPING(FXMMC)=1 THEN '/' ELSE
            <choose>
                <when test="fshxydm!=null and fshxydm!=''">#{fshxydm}</when>
                <otherwise>FNSRSBH</otherwise>
            </choose>
            END FSHXYDM,
            ROUND(SUM(FGCZJWY), 2) AS fgczjwy,
            ROUND(SUM(fcsZzs), 2) AS fcsZzs,
            ROUND(SUM(fcsQysds), 2) AS fcsQysds,
            ROUND(SUM(fcsYhs), 2) AS fcsYhs,
            ROUND(SUM(fcsCswhjss), 2) AS fcsCswhjss,
            ROUND(SUM(fcsJyffj), 2) AS fcsJyffj,
            ROUND(SUM(fcsDfjyfj), 2) AS fcsDfjyfj,
            ROUND(SUM(fcsxj), 2) AS fcsxj
        FROM (
            SELECT *
                , COALESCE(fcsZzs, 0) + COALESCE(fcsQysds, 0) + COALESCE(fcsYhs, 0) + COALESCE(fcsCswhjss, 0) + COALESCE(fcsJyffj, 0) + COALESCE(fcsDfjyfj, 0) AS fcsxj
            FROM (
            SELECT *
                , fcsZzs*0.05 fcsCswhjss, fcsZzs*0.03 fcsJyffj, fcsZzs*0.02 fcsDfjyfj
            FROM (
                SELECT *
                    , CASE WHEN FSGDWLX='本地' THEN FGCZJWY*#{zslZzsBd}   WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslZzsWd} END fcsZzs
                    , CASE WHEN FSGDWLX='本地' THEN NULL WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslQysdsWd} END fcsQysds
                    , CASE WHEN FSGDWLX='本地' THEN FGCZJWY*#{zslYhsBd} WHEN FSGDWLX='外地' THEN FGCZJWY/1.09*#{zslYhsWd} END fcsYhs
                FROM (
                    SELECT FXMMC, FSGDW, TRIM(FSDLXSNHSW) FSDLX
                        , CASE TRIM(FSDLXSNHSW) WHEN '市内' THEN '本地' WHEN '市外' THEN '外地' END FSGDWLX
                        , FGCZJWY
                        , FNSRSBH
                    FROM V_YJ_SGXMYJ <!-- 从原始表处理的视图 -->
                    LEFT JOIN (
                        SELECT * FROM (
                            SELECT FSGDW FSGDW_TMP,FNSRSBH
                                , ROW_NUMBER() over (PARTITION BY FSGDW ORDER BY FRKRQ DESC) rn
                            FROM V_YJ_SGXMYJ LEFT JOIN TB_DW_SRFX_SRFX_MAIN ON FSGDW=FNSRMC
                        ) WHERE rn=1
                    ) tmp ON FSGDW=FSGDW_TMP
                    <where>
                        <if test="fsgdw!=null and fsgdw!=''">
                            AND FSGDW = #{fsgdw}
                        </if>
                        <if test="fxmmc!=null and fxmmc!=''">
                            AND FXMMC LIKE CONCAT('%', #{fxmmc}, '%')
                        </if>
                        <if test="fhtjeMin != null and fhtjeMin != ''">
                            AND FGCZJWY &gt;= #{fhtjeMin}
                        </if>
                        <if test="fhtjeMax != null and fhtjeMax != ''">
                            AND FGCZJWY &lt;= #{fhtjeMax}
                        </if>
                        <if test="fywtjrqS!=null and fywtjrqS!=''">
                            AND TO_DATE(FFZRQ, 'YYYY-MM-DD') >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                        </if>
                        <if test="fywtjrqE!=null and fywtjrqS!=''">
                            AND TO_DATE(FFZRQ, 'YYYY-MM-DD') &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                        </if>
                        <if test="fssqy	!=null and fssqy!=''">
                            AND FSSDQ IN
                            <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="fsgdwlxStr!=null and fsgdwlxStr!=''">
                            AND CASE TRIM(FSDLXSNHSW) WHEN '市内' THEN '1' WHEN '市外' THEN '2' END IN
                            <foreach collection="fsgdwlxStr.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </where>
                )
            )
            )
        )
        GROUP BY ROLLUP(FXMMC)
        ORDER BY fgrouping desc, FSGDW, FXMMC
    </select>

    <!-- 中标企业工程项目预警 -->
    <select id="zbqygcxmyj" resultType="java.util.Map">
        WITH
        prediction AS (
            SELECT *
                , fcsZzs*0.05 fcsCswhjss, fcsZzs*0.03 fcsJyffj, fcsZzs*0.02 fcsDfjyfj
            FROM (
                SELECT *
                    , CASE WHEN FSGDWLX='本地' THEN FZBJWY*#{zslZzsBd}   WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslZzsWd} END fcsZzs
                    , CASE WHEN FSGDWLX='本地' THEN NULL WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslQysdsWd} END fcsQysds
                    , CASE WHEN FSGDWLX='本地' THEN FZBJWY*#{zslYhsBd} WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslYhsWd} END fcsYhs
                FROM (
                    SELECT FZBR, MAX(TRIM(FZBDWSD)) FSDLX
                        , CASE MAX(TRIM(FZBDWSD)) WHEN '市内' THEN '本地' WHEN '市外' THEN '外地' END FSGDWLX
                        , SUM(FZBJWY) FZBJWY
                    FROM V_YJ_ZBQYGCXMYJ <!-- 从原始表处理的视图 -->
                    <where>
                        <if test="fzbr!=null and fzbr!=''">
                            AND FZBR LIKE CONCAT('%', #{fzbr}, '%')
                        </if>
                        <if test="fywtjrqS!=null and fywtjrqS!=''">
                            AND FZBSJ >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                        </if>
                        <if test="fywtjrqE!=null and fywtjrqS!=''">
                            AND FZBSJ &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                        </if>
                        <if test="fssqy	!=null and fssqy!=''">
                            AND FXMSD IN
                            <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="fsgdwlxStr!=null and fsgdwlxStr!=''">
                            AND CASE TRIM(FZBDWSD) WHEN '市内' THEN '1' WHEN '市外' THEN '2' END IN
                            <foreach collection="fsgdwlxStr.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="fcglbStr!=null and fcglbStr!=''">
                            AND TRIM(FCGLB) IN
                            <foreach collection="fcglbStr.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </where>
                    GROUP BY FZBR
                )
            )
        )
        , ss AS (
            SELECT p.FZBR, ROUND(SUM(fzzs)/10000, 2) fhjZzs, ROUND(SUM(fqysds)/10000, 2) fhjQysds, ROUND(SUM(fyhs)/10000, 2) fhjYhs
                , ROUND(SUM(fcswhjss)/10000, 2) fhjCswhjss, ROUND(SUM(fjyffj)/10000, 2) fhjJyffj, ROUND(SUM(fdfjyfj)/10000, 2) fhjDfjyfj
                , MAX(FNSRSBH) FNSRSBH
            FROM prediction p
            INNER JOIN tb_dw_srfx_srfx_main s ON p.FZBR=s.FNSRMC AND s.fzsxm IN ('增值税', '企业所得税', '印花税', '城市维护建设税','教育费附加','地方教育附加')
            <where>
                <if test="fywtjrqS!=null and fywtjrqS!=''">
                    AND s.FRKRQ >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                </if>
                <if test="fywtjrqE!=null and fywtjrqS!=''">
                    AND s.FRKRQ &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                </if>
            </where>
            GROUP BY p.FZBR
        )
        SELECT
            GROUPING(p.FZBR) fgrouping,
            CASE WHEN GROUPING(p.FZBR) THEN '合计' ELSE p.FZBR END FZBR,
            CASE WHEN GROUPING(p.FZBR) THEN '/' ELSE MAX(FNSRSBH) END FSHXYDM,

            ROUND(SUM(p.FZBJWY), 2) AS FZBJWY,

            ROUND(SUM(p.fcsZzs), 2) AS fcsZzs,
            ROUND(SUM(s.fhjZzs), 2) AS fhjZzs,
            ROUND(COALESCE(SUM(p.fcsZzs), 0) - COALESCE(SUM(s.fhjZzs), 0), 2) AS fceZzs,

            ROUND(SUM(p.fcsQysds), 2) AS fcsQysds,
            ROUND(SUM(s.fhjQysds), 2) AS fhjQysds,
            ROUND(COALESCE(SUM(p.fcsQysds), 0) - COALESCE(SUM(s.fhjQysds), 0), 2) AS fceQysds,

            ROUND(SUM(p.fcsYhs), 2) AS fcsYhs,
            ROUND(SUM(s.fhjYhs), 2) AS fhjYhs,
            ROUND(COALESCE(SUM(p.fcsYhs), 0) - COALESCE(SUM(s.fhjYhs), 0), 2) AS fceYhs,

            ROUND(SUM(p.fcsCswhjss), 2) AS fcsCswhjss,
            ROUND(SUM(s.fhjCswhjss), 2) AS fhjCswhjss,
            ROUND(COALESCE(SUM(p.fcsCswhjss), 0) - COALESCE(SUM(s.fhjCswhjss), 0), 2) AS fceCswhjss,

            ROUND(SUM(p.fcsJyffj), 2) AS fcsJyffj,
            ROUND(SUM(s.fhjJyffj), 2) AS fhjJyffj,
            ROUND(COALESCE(SUM(p.fcsJyffj), 0) - COALESCE(SUM(s.fhjJyffj), 0), 2) AS fceJyffj,

            ROUND(SUM(p.fcsDfjyfj), 2) AS fcsDfjyfj,
            ROUND(SUM(s.fhjDfjyfj), 2) AS fhjDfjyfj,
            ROUND(COALESCE(SUM(p.fcsDfjyfj), 0) - COALESCE(SUM(s.fhjDfjyfj), 0), 2) AS fceDfjyfj
        FROM prediction p
        LEFT JOIN ss s ON p.FZBR = s.FZBR
        <where>
            <if test="fshxydm!=null and fshxydm!=''">
                AND FNSRSBH LIKE CONCAT('%', #{fshxydm}, '%')
            </if>
        </where>
        GROUP BY ROLLUP(p.FZBR)
        ORDER BY fgrouping desc
    </select>

    <select id="zbqygcxmyjDetail" resultType="java.util.Map">
        SELECT
            GROUPING(FXMMC) fgrouping,
            CASE WHEN GROUPING(FXMMC)=1 THEN '/' ELSE FZBR END FZBR,
            CASE WHEN GROUPING(FXMMC)=1 THEN '合计' ELSE FXMMC END FXMMC,
            CASE WHEN GROUPING(FXMMC)=1 THEN '/' ELSE
            <choose>
                <when test="fshxydm!=null and fshxydm!=''">#{fshxydm}</when>
                <otherwise>FNSRSBH</otherwise>
            </choose>
            END FSHXYDM,
            ROUND(SUM(FZBJWY), 2) AS fzbjwy,
            ROUND(SUM(fcsZzs), 2) AS fcsZzs,
            ROUND(SUM(fcsQysds), 2) AS fcsQysds,
            ROUND(SUM(fcsYhs), 2) AS fcsYhs,
            ROUND(SUM(fcsCswhjss), 2) AS fcsCswhjss,
            ROUND(SUM(fcsJyffj), 2) AS fcsJyffj,
            ROUND(SUM(fcsDfjyfj), 2) AS fcsDfjyfj,
            ROUND(SUM(fcsxj), 2) AS fcsxj
        FROM (
            SELECT *
                , COALESCE(fcsZzs, 0) + COALESCE(fcsQysds, 0) + COALESCE(fcsYhs, 0) + COALESCE(fcsCswhjss, 0) + COALESCE(fcsJyffj, 0) + COALESCE(fcsDfjyfj, 0) AS fcsxj
            FROM (
                SELECT *
                    , fcsZzs*0.05 fcsCswhjss, fcsZzs*0.03 fcsJyffj, fcsZzs*0.02 fcsDfjyfj
                FROM (
                    SELECT *
                        , CASE WHEN FSGDWLX='本地' THEN FZBJWY*#{zslZzsBd}   WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslZzsWd} END fcsZzs
                        , CASE WHEN FSGDWLX='本地' THEN NULL WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslQysdsWd} END fcsQysds
                        , CASE WHEN FSGDWLX='本地' THEN FZBJWY*#{zslYhsBd} WHEN FSGDWLX='外地' THEN FZBJWY/1.09*#{zslYhsWd} END fcsYhs
                    FROM (
                        SELECT FXMMC, FZBR, TRIM(FZBDWSD) FSDLX
                            , CASE TRIM(FZBDWSD) WHEN '市内' THEN '本地' WHEN '市外' THEN '外地' END FSGDWLX
                            , FZBJWY
                            , FNSRSBH
                        FROM V_YJ_ZBQYGCXMYJ <!-- 从原始表处理的视图 -->
                        LEFT JOIN (
                            SELECT * FROM (
                                SELECT FZBR FZBR_TMP,FNSRSBH
                                    , ROW_NUMBER() over (PARTITION BY FZBR ORDER BY FRKRQ DESC) rn
                                FROM V_YJ_ZBQYGCXMYJ LEFT JOIN TB_DW_SRFX_SRFX_MAIN ON FZBR=FNSRMC
                            ) WHERE rn=1
                        ) tmp ON FZBR=FZBR_TMP
                        <where>
                            <if test="fzbr!=null and fzbr!=''">
                                AND FZBR = #{fzbr}
                            </if>
                            <if test="fxmmc!=null and fxmmc!=''">
                                AND FXMMC LIKE CONCAT('%', #{fxmmc}, '%')
                            </if>
                            <if test="fhtjeMin != null and fhtjeMin != ''">
                                AND FZBJWY &gt;= #{fhtjeMin}
                            </if>
                            <if test="fhtjeMax != null and fhtjeMax != ''">
                                AND FZBJWY &lt;= #{fhtjeMax}
                            </if>
                            <if test="fywtjrqS!=null and fywtjrqS!=''">
                                AND FZBSJ >= TO_DATE(CONCAT(#{fywtjrqS}, '-01'), 'YYYY-MM-DD')
                            </if>
                            <if test="fywtjrqE!=null and fywtjrqS!=''">
                                AND FZBSJ &lt;= LAST_DAY(TO_DATE(#{fywtjrqE}, 'YYYY-MM'))
                            </if>
                            <if test="fssqy	!=null and fssqy!=''">
                                AND FXMSD IN
                                <foreach collection="fssqy.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="fsgdwlxStr!=null and fsgdwlxStr!=''">
                                AND CASE TRIM(FZBDWSD) WHEN '市内' THEN '1' WHEN '市外' THEN '2' END IN
                                <foreach collection="fsgdwlxStr.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                            <if test="fcglbStr!=null and fcglbStr!=''">
                                AND TRIM(FCGLB) IN
                                <foreach collection="fcglbStr.split(',')" item="item" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </where>
                    )
                )
            )
        )
        GROUP BY ROLLUP(FXMMC)
        ORDER BY fgrouping desc
    </select>

    <select id="findTaxDetails_bzb" resultType="hashmap">
        select
        case when grouping(fid) = 1 then '合计' else max(fnsrmc) end as fnsrmc,
        case when grouping(fid) = 1 then '' else max(fnsrsbh) end as fnsrsbh,
        case when grouping(fid) = 1 then '' else max(to_char(substr(frkrq,1,10))) end as frkrq,
        case when grouping(fid) = 1 then '' else max(to_char(substr(fskssqq,1,10))) end as fskssqq,
        case when grouping(fid) = 1 then '' else max(to_char(substr(fskssqz,1,10))) end as fskssqz,
        case when grouping(fid) = 1 then '' else max(fzsxm) end as fzsxm,
        case when grouping(fid) = 1 then '' else max(fzspm) end as fzspm,
        sum(fhj) fhj,
        case when grouping(fid) = 1 then '' else max(fhydl) end as fhydl,
        case when grouping(fid) = 1 then '' else max(fsksx) end as fsksx,
        case when grouping(fid) = 1 then '' else max(fskgk) end as fskgk
        FROM
        tb_dw_srfx_srfx_main
        where 1=1
        <if test="fnsrmc != null and fnsrmc != ''">and
            fnsrmc like CONCAT('%' , #{fnsrmc} , '%')
        </if>
        <if test="fzsxmList != null and fzsxmList.size>0">
            and fzsxm in
            <foreach collection="fzsxmList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="fskssqq_s != null and fskssqq_s != ''">and
            fskssqq BETWEEN to_date(concat(#{fskssqq_s},'-01'), 'YYYY-MM-DD') AND
            last_day(to_date(#{fskssqq_e}, 'YYYY-MM'))
        </if>
        <if test="frkrq_s != null and frkrq_s != ''">and
            frkrq BETWEEN to_date(concat(#{frkrq_s},'-01'), 'YYYY-MM-DD') AND
            last_day(to_date(#{frkrq_e}, 'YYYY-MM'))
        </if>
        <!--土地出让预警查询条件-->
        <if test="tdcryj != null and tdcryj != ''">and
            fzspm like CONCAT('%' , #{tdcryj} , '%')
        </if>
        group by rollup(fid)
        order by fzsxm
    </select>
</mapper>
