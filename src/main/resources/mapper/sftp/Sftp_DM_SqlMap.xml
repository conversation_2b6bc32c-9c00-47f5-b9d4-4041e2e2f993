<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Wed Feb 26 18:51:09 CST 2025-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.sftp.mapper.SftpMapper">

    <insert id="insertDzb" parameterType="java.util.HashMap">
        insert into dzb(filename)values(#{filename})
    </insert>

    <select id="findDzbWjm" resultType="java.util.HashMap">
        select filename from dzb
				 MINUS
		select filename from wjm
    </select>

    <insert id="insertWjm" parameterType="java.util.HashMap">
        insert into wjm(filename)values(#{filename})
    </insert>

    <insert id="insertCapitalMarket" parameterType="com.hnbp.local.sftp.model.Company">
        insert into m_t_domesticCapitalMarket
        (fid,fsjjhwym,f_sys_year,f_sys_month,fztsfdm,fqymc,ftyshxydm,fzch,ftmc,fzt,fdjrq,ffddbfzr,fjjlx,fqylx,fzczb,userid,fcreator,fcreatetime)
        values
        (DBMS_RANDOM.STRING('U', 32),#{uuid},#{fSysYear},
        #{fSysMonth},#{pripid},#{entName},#{uniSCID},#{regNO},#{entTypeName},#{entState},#{estDate},
        #{legal},#{entTypeName},#{entTypeName},#{regCap},'b8cd5040e36549e18460b0875917a32d10000008','工商局',sysdate)
    </insert>

    <insert id="insertTransferInfo" parameterType="com.hnbp.local.sftp.model.Righttramsfer">
        insert into b_t_shareTransferInfo
        (fid,fqymc,f_sys_year,f_sys_month,fqyshxxdm,fbgrq,fbgqgdqk,fsd,fbghgdqk,userid,fcreator,fcreatetime)
        values
        (DBMS_RANDOM.STRING('U', 32),#{oldinv},#{fSysYear},
         #{fSysMonth},#{uniSCID},#{belongDate},#{oldinvtype_CN},'',#{newinvtype_CN},'b8cd5040e36549e18460b0875917a32d10000008','工商局',sysdate)
    </insert>

    <delete id="deleteDzb">
        delete from dzb
    </delete>

</mapper>