Manifest-Version: 1.0
Created-By: <PERSON>ven JAR Plugin 3.4.1
Build-Jdk-Spec: 11
Class-Path: lib/spring-cloud-starter-alibaba-nacos-discovery-2021.0.5.0.
 jar lib/spring-cloud-alibaba-commons-2021.0.5.0.jar lib/nacos-client-2.
 2.0.jar lib/nacos-auth-plugin-2.2.0.jar lib/nacos-encryption-plugin-2.2
 .0.jar lib/commons-codec-1.15.jar lib/jackson-core-2.13.5.jar lib/httpa
 syncclient-4.1.5.jar lib/httpcore-4.4.16.jar lib/httpcore-nio-4.4.16.ja
 r lib/httpclient-4.5.14.jar lib/simpleclient-0.15.0.jar lib/simpleclien
 t_tracer_otel-0.15.0.jar lib/simpleclient_tracer_common-0.15.0.jar lib/
 simpleclient_tracer_otel_agent-0.15.0.jar lib/snakeyaml-1.30.jar lib/sp
 ring-context-support-1.0.11.jar lib/spring-cloud-commons-3.1.7.jar lib/
 spring-security-crypto-5.7.11.jar lib/spring-cloud-context-3.1.7.jar li
 b/spring-cloud-starter-alibaba-nacos-config-2021.0.5.0.jar lib/slf4j-ap
 i-1.7.36.jar lib/knife4j-openapi3-spring-boot-starter-4.4.0.jar lib/kni
 fe4j-core-4.4.0.jar lib/knife4j-openapi3-ui-4.4.0.jar lib/springdoc-ope
 napi-ui-1.7.0.jar lib/springdoc-openapi-webmvc-core-1.7.0.jar lib/sprin
 gdoc-openapi-common-1.7.0.jar lib/swagger-core-2.2.9.jar lib/jackson-da
 taformat-yaml-2.13.5.jar lib/swagger-annotations-2.2.9.jar lib/swagger-
 models-2.2.9.jar lib/swagger-ui-4.18.2.jar lib/biaopu-common-shiro-6.0.
 5-20250626.jar lib/shiro-core-1.13.0.jar lib/shiro-lang-1.13.0.jar lib/
 shiro-cache-1.13.0.jar lib/shiro-crypto-hash-1.13.0.jar lib/shiro-crypt
 o-core-1.13.0.jar lib/shiro-crypto-cipher-1.13.0.jar lib/shiro-config-c
 ore-1.13.0.jar lib/shiro-config-ogdl-1.13.0.jar lib/shiro-event-1.13.0.
 jar lib/shiro-web-1.13.0.jar lib/encoder-1.2.2.jar lib/shiro-spring-1.1
 3.0.jar lib/biaopu-common-core-6.0.5-20250626.jar lib/spring-cloud-star
 ter-openfeign-3.1.8.jar lib/spring-cloud-openfeign-core-3.1.8.jar lib/s
 pring-boot-starter-aop-2.7.18.jar lib/aspectjweaver-1.9.7.jar lib/feign
 -form-spring-3.8.0.jar lib/feign-form-3.8.0.jar lib/commons-fileupload-
 1.5.jar lib/feign-core-11.10.jar lib/feign-slf4j-11.10.jar lib/spring-c
 loud-starter-loadbalancer-3.1.7.jar lib/spring-cloud-loadbalancer-3.1.7
 .jar lib/reactor-core-3.4.34.jar lib/reactive-streams-1.0.4.jar lib/rea
 ctor-extra-3.4.10.jar lib/spring-boot-starter-cache-2.7.18.jar lib/evic
 tor-1.0.0.jar lib/spring-context-support-5.3.31.jar lib/spring-beans-5.
 3.39.jar lib/spring-context-5.3.39.jar lib/spring-core-5.3.39.jar lib/s
 pring-jcl-5.3.31.jar lib/spring-web-5.3.39.jar lib/transmittable-thread
 -local-2.14.3.jar lib/pagehelper-spring-boot-starter-2.1.0.jar lib/page
 helper-spring-boot-autoconfigure-2.1.0.jar lib/pagehelper-6.1.0.jar lib
 /jsqlparser-4.9.jar lib/spring-boot-starter-validation-2.7.18.jar lib/t
 omcat-embed-el-9.0.83.jar lib/hibernate-validator-6.2.5.Final.jar lib/j
 akarta.validation-api-2.0.2.jar lib/jboss-logging-3.4.3.Final.jar lib/c
 lassmate-1.5.1.jar lib/jackson-databind-2.13.5.jar lib/jackson-annotati
 ons-2.13.5.jar lib/jjwt-0.9.1.jar lib/jaxb-api-2.3.1.jar lib/javax.acti
 vation-api-1.2.0.jar lib/commons-lang3-3.12.0.jar lib/commons-io-2.16.1
 .jar lib/pinyin4j-2.5.1.jar lib/poi-ooxml-5.3.0.jar lib/poi-5.3.0.jar l
 ib/commons-math3-3.6.1.jar lib/SparseBitSet-1.3.jar lib/poi-ooxml-lite-
 5.3.0.jar lib/xmlbeans-5.2.1.jar lib/commons-compress-1.26.2.jar lib/cu
 rvesapi-1.08.jar lib/log4j-api-2.17.2.jar lib/commons-collections4-4.4.
 jar lib/javax.servlet-api-4.0.1.jar lib/swagger-annotations-1.6.2.jar l
 ib/mybatis-spring-boot-starter-2.3.2.jar lib/spring-boot-starter-2.7.18
 .jar lib/spring-boot-2.7.18.jar lib/spring-boot-starter-logging-2.7.18.
 jar lib/logback-classic-1.2.12.jar lib/logback-core-1.2.12.jar lib/log4
 j-to-slf4j-2.17.2.jar lib/jul-to-slf4j-1.7.36.jar lib/jakarta.annotatio
 n-api-1.3.5.jar lib/spring-boot-starter-jdbc-2.7.18.jar lib/HikariCP-4.
 0.3.jar lib/spring-jdbc-5.3.31.jar lib/spring-tx-5.3.31.jar lib/mybatis
 -spring-boot-autoconfigure-2.3.2.jar lib/mybatis-3.5.14.jar lib/mybatis
 -spring-2.1.2.jar lib/druid-spring-boot-starter-1.2.23.jar lib/druid-1.
 2.23.jar lib/spring-boot-autoconfigure-2.7.18.jar lib/jaxb-runtime-2.3.
 9.jar lib/jakarta.xml.bind-api-2.3.3.jar lib/txw2-2.3.9.jar lib/istack-
 commons-runtime-3.0.12.jar lib/jakarta.activation-1.2.2.jar lib/mapper-
 spring-boot-starter-2.1.5.jar lib/mapper-core-1.1.5.jar lib/persistence
 -api-1.0.jar lib/mapper-base-1.1.5.jar lib/mapper-weekend-1.1.5.jar lib
 /mapper-spring-1.1.5.jar lib/mapper-extra-1.1.5.jar lib/mapper-spring-b
 oot-autoconfigure-2.1.5.jar lib/spring-boot-starter-web-2.7.18.jar lib/
 spring-boot-starter-json-2.7.18.jar lib/jackson-datatype-jdk8-2.13.5.ja
 r lib/jackson-datatype-jsr310-2.13.5.jar lib/jackson-module-parameter-n
 ames-2.13.5.jar lib/spring-boot-starter-tomcat-2.7.18.jar lib/tomcat-em
 bed-core-9.0.83.jar lib/tomcat-embed-websocket-9.0.83.jar lib/spring-we
 bmvc-5.3.39.jar lib/spring-aop-5.3.39.jar lib/spring-expression-5.3.39.
 jar lib/biaopu-common-security-6.0.5-20250626.jar lib/biaopu-api-system
 -6.0.5-20250626.jar lib/biaopu-common-redis-6.0.5-20250626.jar lib/spri
 ng-boot-starter-data-redis-2.7.18.jar lib/spring-data-redis-2.7.18.jar 
 lib/spring-data-keyvalue-2.7.18.jar lib/spring-data-commons-2.7.18.jar 
 lib/spring-oxm-5.3.31.jar lib/lettuce-core-6.1.10.RELEASE.jar lib/netty
 -common-4.1.101.Final.jar lib/netty-handler-4.1.101.Final.jar lib/netty
 -resolver-4.1.101.Final.jar lib/netty-buffer-4.1.101.Final.jar lib/nett
 y-transport-native-unix-common-4.1.101.Final.jar lib/netty-codec-4.1.10
 1.Final.jar lib/netty-transport-4.1.101.Final.jar lib/hutool-all-5.8.36
 .jar lib/fastjson2-2.0.52.jar lib/DmJdbcDriver18-8.1.3.140.jar lib/jsch
 -0.1.55.jar lib/json-lib-2.4-jdk15.jar lib/ezmorph-1.0.6.jar lib/common
 s-lang-2.5.jar lib/commons-beanutils-1.9.2.jar lib/commons-collections-
 3.2.1.jar lib/commons-logging-1.2.jar lib/gson-2.8.9.jar lib/spring-clo
 ud-starter-bootstrap-3.1.7.jar lib/spring-cloud-starter-3.1.7.jar lib/s
 pring-security-rsa-1.0.11.RELEASE.jar lib/bcpkix-jdk15on-1.69.jar lib/b
 cprov-jdk15on-1.69.jar lib/bcutil-jdk15on-1.69.jar
Main-Class: org.springframework.boot.loader.PropertiesLauncher
Start-Class: com.hnbp.local.BpLocalApplication
Spring-Boot-Version: 2.7.18
Spring-Boot-Classes: BOOT-INF/classes/
Spring-Boot-Lib: BOOT-INF/lib/
Spring-Boot-Classpath-Index: BOOT-INF/classpath.idx
Spring-Boot-Layers-Index: BOOT-INF/layers.idx

