# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-biaopu-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
