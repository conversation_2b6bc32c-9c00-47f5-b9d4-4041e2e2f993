<?xml version="1.0" encoding="UTF-8"?><!--Converted at: Wed Feb 26 18:51:09 CST 2025-->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.czdp.mapper.CzLargeScreenDisplayMapper">


    <!-- 一般公共预算收支情况表 -->
    <select id="getIncomeAndExpensesTable" parameterType="java.util.Map" resultType="java.util.Map">
        WITH t_base_data AS (
            select FSSDQ, FKMMC, FJE,FNCYSJE,
                   FJE - COALESCE(LAG(FJE) OVER (PARTITION BY FSSDQ, FKMMC, FXMLX, fnf ORDER BY CAST(fyf AS INT)), 0) AS fjedy,
                   FXMLX , fnf, fyf
            from (
                     select
                         yb.FSSDQ, yb.FKMMC, FJE,yb.FXMLX ,ys.FNCYSJE,  yb.fnf, yb.fyf
                     from TB_DM_ZHZS_CZSZYB_GEN yb
                              left join TB_DW_CZSZ_YSSZMB_GEN ys on ys.FKMMC = yb.FKMMC and ys.FSSDQ = yb.FSSDQ and ys.fnf = yb.fnf
                     where yb.FKMMC in ('一般公共预算收入合计','税收收入','非税收入','一般公共预算支出合计')
                 )
        ), t_row2column AS (
            SELECT
                FSSDQ, fnf, fyf,
                SUM(CASE WHEN FKMMC = '一般公共预算收入合计' AND FXMLX ='收入' THEN FJE ELSE 0 END) AS ybggyssrhj,
                SUM(CASE WHEN FKMMC = '一般公共预算收入合计' AND FXMLX ='收入' THEN FJEDY ELSE 0 END) AS ybggyssrhjdy,
                SUM(CASE WHEN FKMMC = '一般公共预算收入合计' AND FXMLX ='收入' THEN FNCYSJE ELSE 0 END) AS ysmb,
                SUM(CASE WHEN FKMMC = '税收收入' AND FXMLX ='收入' THEN FJE ELSE 0 END) AS sssr,
                SUM(CASE WHEN FKMMC = '非税收入' AND FXMLX ='收入' THEN FJE ELSE 0 END) AS fssr,
                SUM(CASE WHEN FKMMC = '一般公共预算支出合计' AND FXMLX ='支出' THEN FJE ELSE 0 END) AS ybggyszchj,
                SUM(CASE WHEN FKMMC = '一般公共预算支出合计' AND FXMLX ='支出' THEN FJEDY ELSE 0 END) AS ybggyszchjdy

            FROM t_base_data
            GROUP BY FSSDQ, fnf, fyf
            ORDER BY FSSDQ, fnf, fyf
        )
        select jn.fssdq, jn.ybggyssrhj,jn.ybggyssrhjdy,jn.ysmb,jn.sssr,jn.fssr,jn.ybggyszchj,jn.ybggyszchjdy,
               qn.ybggyssrhj ybggyssrhjqn,qn.ybggyszchj ybggyszchjqn,
               CASE WHEN jn.ysmb IS NOT NULL AND jn.ysmb != 0 THEN ROUND(jn.ybggyssrhj / jn.ysmb * 100, 2) ELSE 0 END AS yszb,
               CASE WHEN jn.ybggyssrhj IS NOT NULL AND jn.ybggyssrhj != 0 THEN ROUND(jn.sssr / jn.ybggyssrhj * 100, 2) ELSE 0 END AS sszb,
               CASE WHEN qn.ybggyssrhj IS NOT NULL AND qn.ybggyssrhj != 0 THEN ROUND((jn.ybggyssrhj - qn.ybggyssrhj) / qn.ybggyssrhj * 100, 2) ELSE 0 END AS ybggyssrhjzf,
               CASE WHEN qn.ybggyszchj IS NOT NULL AND qn.ybggyszchj != 0 THEN ROUND((jn.ybggyszchj - qn.ybggyszchj) / qn.ybggyszchj * 100, 2) ELSE 0 END AS ybggyszchjzf,
               jn.fnf, jn.fyf
        from t_row2column jn
        left join t_row2column qn on jn.fssdq = qn.fssdq and jn.fnf = qn.fnf+1 and jn.fyf = qn.fyf
        where 1=1
        <if test="fssdq != null and fssdq != ''">
            and jn.FSSDQ = #{fssdq}
        </if>
        <if test="fyear != null and fyear != ''">
            and jn.fnf = #{fyear}
        </if>
        <if test="fmonth != null and fmonth != ''">
            and jn.fyf = #{fmonth}
        </if>

    </select>

</mapper>